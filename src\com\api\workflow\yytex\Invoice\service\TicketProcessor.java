package com.api.workflow.yytex.Invoice.service;

import com.api.workflow.yytex.Invoice.util.InvoiceUtil;
import org.json.JSONException;
import org.json.JSONObject;

import static com.api.workflow.yytex.Invoice.constants.TicketTypeConstants.VAT_TYPES;

/**
 * 票据处理器 - 统一处理各类票据的字段提取逻辑
 * 消除重复代码，提高代码复用性
 */
public class TicketProcessor {
    private final com.alibaba.fastjson.JSONObject result;
    private final String type;
    private final boolean isVatInvoice;

    public TicketProcessor(com.alibaba.fastjson.JSONObject result, String type) {
        this.result = result;
        this.type = type;
        this.isVatInvoice = VAT_TYPES.contains(type);
    }

    /**
     * 处理交通票据
     */
    public JSONObject processTransportTicket() throws JSONException {
        JSONObject ticket = new JSONObject();
        ticket.put("ticketType", type);

        // 提取共有字段
        extractCommonFields(ticket);
        
        // 提取交通工具编号
        extractTransportNumber(ticket);
        
        // 提取座位类型
        extractSeatType(ticket);
        
        // 提取金额
        extractAmount(ticket);
        
        // 提取票号或发票号
        extractInvoiceInfo(ticket);
        
        // 兼容前端的字段名 - 将transportNum复制到trainNum
        if (ticket.has("transportNum")) {
            ticket.put("trainNum", ticket.getString("transportNum"));
        }

        return ticket;
    }

    /**
     * 处理打车票据
     */
    public JSONObject processTaxiTicket() throws JSONException {
        JSONObject taxiData = new JSONObject();
        taxiData.put("ticketType", type);

        // 提取购买方信息
        extractPurchaserInfo(taxiData);
        
        // 提取项目名称
        extractTaxiItemName(taxiData);
        
        // 提取金额
        extractTaxiAmount(taxiData);
        
        // 提取发票号/票号和日期
        extractTaxiInvoiceInfo(taxiData);
        
        // 特殊字段 - 出租车号牌
        if (result.containsKey("TaxiNum")) {
            InvoiceUtil.extractField(result, "TaxiNum", taxiData, "taxiNumber");
        }

        // 特殊字段 - 行程信息
        String journeyTime = InvoiceUtil.extractJourneyTime(result);
        if (!journeyTime.isEmpty()) {
            taxiData.put("journeyTime", journeyTime);
        }

        return taxiData;
    }

    /**
     * 处理餐费票据
     */
    public JSONObject processMealTicket() throws JSONException {
        JSONObject mealData = new JSONObject();
        mealData.put("ticketType", type);

        // 提取购买方信息
        extractPurchaserInfo(mealData);
        
        // 提取项目名称
        extractMealItemName(mealData);
        
        // 提取金额
        extractMealAmount(mealData);
        
        // 提取发票号和日期
        extractMealInvoiceInfo(mealData);

        return mealData;
    }

    /**
     * 处理增值税发票
     */
    public JSONObject processVatTicket() throws JSONException {
        JSONObject vatData = new JSONObject();
        vatData.put("ticketType", type);

        // 提取购买方信息
        InvoiceUtil.extractField(result, "PurchaserName", vatData, "purchaserName");
        InvoiceUtil.extractField(result, "PurchaserRegisterNum", vatData, "purchaserRegisterNum");

        // 提取商品名称
        String commodityNames = InvoiceUtil.extractCommodityNames(result, "CommodityName");
        vatData.put("commodityName", commodityNames);

        // 提取金额
        extractVatAmount(vatData);
        
        // 提取发票号和日期
        InvoiceUtil.extractField(result, "InvoiceNum", vatData, "invoiceNum");
        InvoiceUtil.extractField(result, "InvoiceDate", vatData, "invoiceDate");
        InvoiceUtil.extractField(result, "SellerName", vatData, "sellerName");

        return vatData;
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 提取交通票据的共有字段
     */
    private void extractCommonFields(JSONObject ticket) throws JSONException {
        InvoiceUtil.extractField(result, "name", ticket, "name");
        InvoiceUtil.extractDateTime(result, ticket);
        InvoiceUtil.extractField(result, "starting_station", ticket, "startStation");
        InvoiceUtil.extractField(result, "destination_station", ticket, "destStation");
    }

    /**
     * 提取交通工具编号
     */
    private void extractTransportNumber(JSONObject ticket) throws JSONException {
        if ("train_ticket".equals(type)) {
            InvoiceUtil.extractField(result, "train_num", ticket, "transportNum");
        } else if ("air_ticket".equals(type)) {
            String flightInfo = InvoiceUtil.extractCarrierAndFlight(result);
            if (flightInfo != null) {
                ticket.put("transportNum", flightInfo);
            }
        } else if ("bus_ticket".equals(type)) {
            InvoiceUtil.extractField(result, "bus_num", ticket, "transportNum");
        } else if ("ferry_ticket".equals(type)) {
            InvoiceUtil.extractField(result, "ferry_num", ticket, "transportNum");
        } else if (VAT_TYPES.contains(type)) {
            // 检查是否为租车服务
            if (InvoiceUtil.isCommodityRentalCarService(result)) {
                // 对于经营租赁/租车服务的增值税发票，添加特殊标识
                ticket.put("transportNum", "租车服务");

                // 补充出发地和目的地（如果未提供）
                if (!ticket.has("startStation")) {
                    ticket.put("startStation", "租车地点");
                }
                if (!ticket.has("destStation")) {
                    ticket.put("destStation", "返还地点");
                }
            }
        }

        if (!ticket.has("transportNum")) {
            if (!InvoiceUtil.extractField(result, "vehicle_num", ticket, "transportNum") &&
                    !InvoiceUtil.extractField(result, "transport_num", ticket, "transportNum")) {
                ticket.put("transportNum", InvoiceUtil.formatTicketType(type));
            }
        }
    }

    /**
     * 提取座位类型
     */
    private void extractSeatType(JSONObject ticket) throws JSONException {
        try {
            // 检查是否是租车服务发票
            if (VAT_TYPES.contains(type) && InvoiceUtil.isCommodityRentalCarService(result)) {
                ticket.put("seatType", "经营租赁服务");
            } else {
                String seatType = InvoiceUtil.extractSeatType(result, type);
                ticket.put("seatType", seatType);
            }
        } catch (Exception e) {
            // 座位类型提取失败，使用默认值
            ticket.put("seatType", InvoiceUtil.formatTicketType(type));
        }
    }

    /**
     * 提取金额（交通票据）
     */
    private void extractAmount(JSONObject ticket) throws JSONException {
        String amount = "";
        // 对于增值税发票，使用AmountInFiguers字段
        if (VAT_TYPES.contains(type) && result.containsKey("AmountInFiguers")) {
            amount = InvoiceUtil.extractFirstWord(result, "AmountInFiguers");
        } else {
            amount = InvoiceUtil.extractAmount(result);
        }
        ticket.put("amount", amount);
    }

    /**
     * 提取票号或发票号（交通票据）
     */
    private void extractInvoiceInfo(JSONObject ticket) throws JSONException {
        if (VAT_TYPES.contains(type)) {
            InvoiceUtil.extractField(result, "InvoiceNum", ticket, "invoiceNum");
            InvoiceUtil.extractField(result, "InvoiceDate", ticket, "invoiceDate");
            InvoiceUtil.extractField(result, "SellerName", ticket, "sellerName");

            // 增值税发票中提取购买方信息
            InvoiceUtil.extractField(result, "PurchaserName", ticket, "purchaserName");

            // 提取商品名称，用于显示为备注
            if (result.containsKey("CommodityName")) {
                String commodityNames = InvoiceUtil.extractCommodityNames(result, "CommodityName");
                ticket.put("commodityName", commodityNames);
            }
        } else {
            if (!InvoiceUtil.extractField(result, "ticket_number", ticket, "invoiceNum")) {
                InvoiceUtil.extractField(result, "invoice_num", ticket, "invoiceNum");
            }
        }
    }

    /**
     * 提取购买方信息
     */
    private void extractPurchaserInfo(JSONObject data) throws JSONException {
        if (isVatInvoice) {
            InvoiceUtil.extractField(result, "PurchaserName", data, "purchaserName");
            InvoiceUtil.extractField(result, "PurchaserRegisterNum", data, "purchaserTaxNum");
        } else {
            InvoiceUtil.extractField(result, "buyer", data, "purchaserName");
            InvoiceUtil.extractField(result, "buyer_tax_id", data, "purchaserTaxNum");
        }
    }

    /**
     * 提取打车票据项目名称
     */
    private void extractTaxiItemName(JSONObject taxiData) throws JSONException {
        String itemName = "";
        if (isVatInvoice && result.containsKey("CommodityName")) {
            itemName = InvoiceUtil.extractCommodityNames(result, "CommodityName");
        } else if (result.containsKey("service")) {
            itemName = InvoiceUtil.extractFirstWord(result, "service");
        } else if (result.containsKey("item")) {
            itemName = InvoiceUtil.extractFirstWord(result, "item");
        }
        taxiData.put("itemName", itemName);
    }

    /**
     * 提取打车票据金额
     */
    private void extractTaxiAmount(JSONObject taxiData) throws JSONException {
        String amount = "";
        if (isVatInvoice && result.containsKey("AmountInFiguers")) {
            amount = InvoiceUtil.extractFirstWord(result, "AmountInFiguers");
        } else if (result.containsKey("TotalFare")) {
            amount = InvoiceUtil.extractFirstWord(result, "TotalFare");
        } else {
            amount = InvoiceUtil.extractAmount(result);
        }
        taxiData.put("amount", amount);
    }

    /**
     * 提取打车票据发票信息
     */
    private void extractTaxiInvoiceInfo(JSONObject taxiData) throws JSONException {
        if (isVatInvoice) {
            InvoiceUtil.extractField(result, "InvoiceNum", taxiData, "invoiceNum");
            InvoiceUtil.extractField(result, "InvoiceDate", taxiData, "invoiceDate");
            InvoiceUtil.extractField(result, "SellerName", taxiData, "sellerName");
        } else {
            if (!InvoiceUtil.extractField(result, "InvoiceNum", taxiData, "invoiceNum")) {
                InvoiceUtil.extractField(result, "invoice_num", taxiData, "invoiceNum");
            }
            if (!InvoiceUtil.extractField(result, "Date", taxiData, "invoiceDate")) {
                InvoiceUtil.extractField(result, "date", taxiData, "invoiceDate");
            }
            if (!InvoiceUtil.extractField(result, "Location", taxiData, "sellerName")) {
                InvoiceUtil.extractField(result, "seller", taxiData, "sellerName");
            }
        }
    }

    /**
     * 提取餐费票据项目名称
     */
    private void extractMealItemName(JSONObject mealData) throws JSONException {
        String itemName = "";
        if (isVatInvoice && result.containsKey("CommodityName")) {
            itemName = InvoiceUtil.extractCommodityNames(result, "CommodityName");
        } else if (result.containsKey("item")) {
            // 原有的item处理逻辑
            itemName = InvoiceUtil.extractItemNames(result, "item");
        }
        mealData.put("itemName", itemName);
    }

    /**
     * 提取餐费票据金额
     */
    private void extractMealAmount(JSONObject mealData) throws JSONException {
        String amount = "";
        if (isVatInvoice && result.containsKey("AmountInFiguers")) {
            amount = InvoiceUtil.extractFirstWord(result, "AmountInFiguers");
        } else {
            amount = InvoiceUtil.extractAmount(result);
        }
        mealData.put("amount", amount);
    }

    /**
     * 提取餐费票据发票信息
     */
    private void extractMealInvoiceInfo(JSONObject mealData) throws JSONException {
        if (isVatInvoice) {
            InvoiceUtil.extractField(result, "InvoiceNum", mealData, "invoiceNum");
            InvoiceUtil.extractField(result, "InvoiceDate", mealData, "invoiceDate");
            InvoiceUtil.extractField(result, "SellerName", mealData, "sellerName");
        } else {
            InvoiceUtil.extractField(result, "invoice_num", mealData, "invoiceNum");
            InvoiceUtil.extractField(result, "date", mealData, "invoiceDate");
            InvoiceUtil.extractField(result, "seller", mealData, "sellerName");
        }
    }

    /**
     * 提取增值税发票金额
     */
    private void extractVatAmount(JSONObject vatData) throws JSONException {
        String amount = "";
        if (result.containsKey("AmountInFiguers")) {
            amount = InvoiceUtil.extractFirstWord(result, "AmountInFiguers");
        } else {
            amount = InvoiceUtil.extractAmount(result);
        }
        vatData.put("amount", amount);
    }
}
