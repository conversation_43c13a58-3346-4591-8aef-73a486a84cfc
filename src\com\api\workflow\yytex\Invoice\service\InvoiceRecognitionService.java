package com.api.workflow.yytex.Invoice.service;

import com.api.workflow.yytex.Invoice.constants.ErrorMessages;
import com.api.workflow.yytex.Invoice.constants.ReimbursementResult;
import com.api.workflow.yytex.Invoice.constants.ServiceTypeConstants;
import com.api.workflow.yytex.Invoice.constants.TicketTypeConstants;
import com.api.workflow.yytex.Invoice.util.InvoiceUtil;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import weaver.conn.RecordSet;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.api.workflow.yytex.Invoice.constants.TicketTypeConstants.VAT_TYPES;

/**
 * 发票识别服务类 - 处理核心业务逻辑
 */
public class InvoiceRecognitionService {
    // 常量定义
    private final String SERVER_IP;
    private final String TICKET_API_URL;

    // 华住会常量
    private static final String HUAZHUHUI_YES = "0";

    // 发票验证器
    private InvoiceValidator invoiceValidator = new InvoiceValidator();

    /**
     * 构造函数 - 接收服务器配置
     */
    public InvoiceRecognitionService(String serverIp, String ticketApiUrl) {
        this.SERVER_IP = serverIp;
        this.TICKET_API_URL = ticketApiUrl;
    }

    public JSONObject processAttachments(
            String transportAttachIds, String vatAttachIds,
            String mealAttachIds, String taxiAttachIds,
            String positionCode, int days, String city, String requestid,
            int numberOfTravelers, String isHuazhuhui) throws JSONException {
        try {
            // 校验至少有一种票据
            if (InvoiceUtil.isEmpty(transportAttachIds) &&
                    InvoiceUtil.isEmpty(vatAttachIds) &&
                    InvoiceUtil.isEmpty(mealAttachIds) &&
                    InvoiceUtil.isEmpty(taxiAttachIds)) {
                JSONObject response = new JSONObject();
                response.put("success", false);
                response.put("message", ErrorMessages.NO_ATTACHMENTS);
                return response;
            }

            // 调用处理方法，传递所有参数
            JSONObject result = processAllAttachmentIds(transportAttachIds, vatAttachIds,
                    mealAttachIds, taxiAttachIds,
                    positionCode, days, city, requestid,
                    numberOfTravelers, isHuazhuhui);

            return result;

        } catch (Exception e) {
            JSONObject response = new JSONObject();
            response.put("success", false);
            response.put("message", "处理失败: " + e.getMessage());
            return response;
        }
    }

    //处理所有附件ID
    private JSONObject processAllAttachmentIds(String transportAttachIds, String vatAttachIds,
                                               String mealAttachIds, String taxiAttachIds,
                                               String positionCode, int days, String city,
                                               String requestid, int numberOfTravelers, String isHuazhuhui) throws JSONException {
        JSONObject response = new JSONObject();
        RecordSet rs = new RecordSet();

        try {
            // 处理交通票附件
            List<Integer> transportFileIds = InvoiceUtil.getAttachmentIds(rs, transportAttachIds);
            List<String> transportFileUrls = InvoiceUtil.getFileDownUrls(transportFileIds, SERVER_IP);

            // 处理发票附件
            List<Integer> vatFileIds = InvoiceUtil.getAttachmentIds(rs, vatAttachIds);
            List<String> vatFileUrls = InvoiceUtil.getFileDownUrls(vatFileIds, SERVER_IP);

            // 处理餐费票据附件
            List<Integer> mealFileIds = InvoiceUtil.getAttachmentIds(rs, mealAttachIds);
            List<String> mealFileUrls = InvoiceUtil.getFileDownUrls(mealFileIds, SERVER_IP);

            // 处理打车票据附件
            List<Integer> taxiFileIds = InvoiceUtil.getAttachmentIds(rs, taxiAttachIds);
            List<String> taxiFileUrls = InvoiceUtil.getFileDownUrls(taxiFileIds, SERVER_IP);

            if (transportFileUrls.isEmpty() && vatFileUrls.isEmpty() &&
                    mealFileUrls.isEmpty() && taxiFileUrls.isEmpty()) {
                response.put("success", false);
                response.put("message", ErrorMessages.NO_VALID_FILES);
                return response;
            }

            // 使用InvoiceUtil构建请求JSON
            String jsonBody = InvoiceUtil.buildTicketJsonRequestBody(
                    transportFileUrls, vatFileUrls, mealFileUrls, taxiFileUrls);

            // 发送识别请求
            weaverjn.util.TicketHttpClient httpClient = new weaverjn.util.TicketHttpClient();
            String recognitionResponse = httpClient.sendTicketData(TICKET_API_URL, jsonBody);

            if (InvoiceUtil.isEmpty(recognitionResponse)) {
                response.put("success", false);
                response.put("message", ErrorMessages.NO_RESPONSE);
                return response;
            }

            // 解析响应
            com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(recognitionResponse);

            if (!jsonObject.getBooleanValue("success")) {
                response.put("success", false);
                response.put("message", jsonObject.getString("message"));
                return response;
            }

            // 提取响应数据
            com.alibaba.fastjson.JSONObject data = jsonObject.getJSONObject("data");

            // 处理票据数据
            double totalTransportAmount = 0.0;
            double totalVatAmount = 0.0;
            double totalMealAmount = 0.0;
            double totalTaxiAmount = 0.0;

            JSONArray transportTickets = new JSONArray();
            JSONArray vatReceipts = new JSONArray();
            JSONArray mealTickets = new JSONArray();
            JSONArray taxiTickets = new JSONArray();

            // 用于收集所有发票号码
            List<String> allInvoiceNumbers = new ArrayList<>();

            // 处理增值税发票数据
            if (data.containsKey("vats")) {
                com.alibaba.fastjson.JSONArray vats = data.getJSONArray("vats");
                if (vats != null && !vats.isEmpty()) {
                    for (int i = 0; i < vats.size(); i++) {
                        com.alibaba.fastjson.JSONObject vat = vats.getJSONObject(i);

                        // 创建发票数据
                        JSONObject vatData = new JSONObject();
                        vatData.put("ticketType", "vat_invoice");
                        vatData.put("purchaserName", vat.getString("purchaserName"));
                        vatData.put("purchaserRegisterNum", vat.getString("purchaserRegisterNum"));

                        // 处理项目名称列表
                        String commodityNames = "";
                        com.alibaba.fastjson.JSONArray commodityArray = vat.getJSONArray("commodityName");
                        if (commodityArray != null && !commodityArray.isEmpty()) {
                            commodityNames = InvoiceUtil.extractArrayWords(commodityArray);
                        }
                        vatData.put("commodityName", commodityNames);

                        // 获取发票金额
                        String amountStr = vat.getString("amountInFiguers");
                        vatData.put("amount", amountStr);

                        // 累加发票金额
                        totalVatAmount = InvoiceUtil.addNumericAmount(totalVatAmount, amountStr);

                        // 获取发票号并保存
                        String invoiceNum = vat.getString("invoiceNum");
                        vatData.put("invoiceNum", invoiceNum);

                        // 添加发票号到列表中
                        if (!InvoiceUtil.isEmpty(invoiceNum)) {
                            allInvoiceNumbers.add(invoiceNum);
                        }

                        vatData.put("invoiceDate", vat.getString("invoiceDate"));
                        vatData.put("sellerName", vat.getString("sellerName"));

                        // 添加到结果集
                        vatReceipts.put(vatData);
                    }
                }
            }

            // 处理multiples数组中的结果 - 根据不同type和ServiceType分类
            if (data.containsKey("multiples")) {
                com.alibaba.fastjson.JSONArray multiples = data.getJSONArray("multiples");
                if (multiples != null && !multiples.isEmpty()) {
                    for (int i = 0; i < multiples.size(); i++) {
                        com.alibaba.fastjson.JSONObject multipleItem = multiples.getJSONObject(i);
                        String type = multipleItem.getString("type");
                        com.alibaba.fastjson.JSONObject result = multipleItem.getJSONObject("result");

                        // 检查ServiceType字段
                        String serviceType = InvoiceUtil.extractFirstWord(result, "ServiceType");

                        // 检查InvoiceTag字段，用于识别客运服务类发票
                        String invoiceTag = InvoiceUtil.extractFirstWord(result, "InvoiceTag");

                        // 检查是否为交通运输服务
                        boolean isTransportService = InvoiceUtil.isCommodityTransportService(result);

                        // 检查是否为租车服务
                        boolean isRentalCarService = InvoiceUtil.isCommodityRentalCarService(result);

                        // 检查是否为成品油发票（私车公用）
                        boolean isFuelInvoice = InvoiceUtil.isFuelInvoice(result, invoiceTag);

                        // 检查是否为通行费发票（私车公用）
                        boolean isTollInvoice = InvoiceUtil.isTollInvoice(result, invoiceTag);

                        // 提取并保存发票号 - 收集所有可能包含发票号的字段
                        String invoiceNum = "";

                        if (result.containsKey("InvoiceNum")) {
                            invoiceNum = InvoiceUtil.extractFirstWord(result, "InvoiceNum");
                        } else if (result.containsKey("invoice_num")) {
                            invoiceNum = InvoiceUtil.extractFirstWord(result, "invoice_num");
                        } else if (result.containsKey("ticket_number")) {
                            invoiceNum = InvoiceUtil.extractFirstWord(result, "ticket_number");
                        }

                        // 如果发票号不为空，添加到列表
                        if (!InvoiceUtil.isEmpty(invoiceNum)) {
                            allInvoiceNumbers.add(invoiceNum);
                        }

                        // 根据类型和服务类型分类处理
                        if (VAT_TYPES.contains(type) && isFuelInvoice) {
                            // 处理成品油增值税发票为私车公用票据
                            totalPrivateCarAmount = processPrivateCarTicket(result, type, privateCarTickets, totalPrivateCarAmount);
                        }
                        else if (VAT_TYPES.contains(type) && isTollInvoice) {
                            // 处理通行费增值税发票为私车公用票据
                            totalPrivateCarAmount = processPrivateCarTicket(result, type, privateCarTickets, totalPrivateCarAmount);
                        }
                        else if (VAT_TYPES.contains(type) && ServiceTypeConstants.DINING.equals(serviceType)) {
                            // 处理餐饮类增值税发票为餐费票据
                            totalMealAmount = processMealTicket(result, type, mealTickets, totalMealAmount);
                        }
                        else if (VAT_TYPES.contains(type) && isRentalCarService) {
                            // 处理租车服务类增值税发票为交通票据
                            totalTransportAmount = processTransportTicket(result, type, transportTickets, totalTransportAmount);
                        }
                        else if (VAT_TYPES.contains(type) &&
                                (ServiceTypeConstants.TRANSPORT.equals(serviceType) ||
                                        ServiceTypeConstants.PASSENGER_TRANSPORT.equals(invoiceTag) ||
                                        isTransportService)) {
                            // 处理交通/客运服务类增值税发票为打车票据
                            totalTaxiAmount = processTaxiTicket(result, type, taxiTickets, totalTaxiAmount);
                        }
                        else if (TicketTypeConstants.TRANSPORT_TYPES.contains(type)) {
                            totalTransportAmount = processTransportTicket(result, type, transportTickets, totalTransportAmount);
                        }
                        else if (TicketTypeConstants.TAXI_TYPES.contains(type)) {
                            totalTaxiAmount = processTaxiTicket(result, type, taxiTickets, totalTaxiAmount);
                        }
                        else if (TicketTypeConstants.MEAL_TYPES.contains(type)) {
                            totalMealAmount = processMealTicket(result, type, mealTickets, totalMealAmount);
                        }
                        else if (VAT_TYPES.contains(type)) {
                            totalVatAmount = processVatTicket(result, type, vatReceipts, totalVatAmount);
                        }
                        else if ("others".equals(type)) {
                            // 对于其他类型，先检查是否为租车服务
                            if (isRentalCarService) {
                                // 租车服务归类为交通票
                                totalTransportAmount = processTransportTicket(result, type, transportTickets, totalTransportAmount);
                            }
                            // 然后尝试通过ServiceType判断
                            else if (ServiceTypeConstants.DINING.equals(serviceType)) {
                                totalMealAmount = processMealTicket(result, type, mealTickets, totalMealAmount);
                            } else if (ServiceTypeConstants.TRANSPORT.equals(serviceType)) {
                                totalTaxiAmount = processTaxiTicket(result, type, taxiTickets, totalTaxiAmount);
                            } else {
                                // 默认放入餐费票据
                                totalMealAmount = processMealTicket(result, type, mealTickets, totalMealAmount);
                            }
                        }
                    }
                }
            }

            // 检查发票号是否重复 - 包括当前表单内的重复和数据库中的重复
            if (!allInvoiceNumbers.isEmpty()) {
                // 调用发票验证器进行检查
                JSONObject checkResult = invoiceValidator.checkDuplicateInvoices(allInvoiceNumbers, requestid);

                // 如果检查结果不是成功，返回错误信息
                if (!checkResult.getBoolean("success")) {
                    return checkResult;
                }
            }

            // 判断是否有各类发票
            boolean hasVatInvoice = !InvoiceUtil.isEmpty(vatAttachIds);

            // 计算报销金额
            double totalActual = totalTransportAmount + totalVatAmount + totalMealAmount + totalTaxiAmount;
            Map<String, Integer> standards = getTravelStandard(city, positionCode);

            // 计算最终报销金额及附加详情
            JSONObject standardInfo = new JSONObject();
            JSONArray calculationProcess = new JSONArray();

            // 使用新的方法计算报销金额和获取计算过程，传入新参数
            ReimbursementResult reimbursementResult = calculateReimbursement(
                    standards, days, hasVatInvoice,
                    totalVatAmount, totalTransportAmount, totalMealAmount, totalTaxiAmount,
                    totalActual, standardInfo, calculationProcess,
                    numberOfTravelers, isHuazhuhui);

            // 构建最终响应 - 包含计算过程和新参数信息
            response.put("success", true);
            response.put("transportTickets", transportTickets);
            response.put("vatReceipts", vatReceipts);
            response.put("mealTickets", mealTickets);
            response.put("taxiTickets", taxiTickets);
            response.put("totalTransportAmount", totalTransportAmount);
            response.put("totalVatAmount", totalVatAmount);
            response.put("totalMealAmount", totalMealAmount);
            response.put("totalTaxiAmount", totalTaxiAmount);
            response.put("totalActualAmount", totalActual);
            response.put("totalExpenseAmount", reimbursementResult.totalExpenseAmount);
            response.put("standardInfo", reimbursementResult.standardInfo);
            response.put("calculationProcess", reimbursementResult.calculationProcess);
            response.put("destination", city);
            response.put("position", InvoiceUtil.mapPositionToName(positionCode));
            response.put("numberOfTravelers", numberOfTravelers);
            response.put("isHuazhuhui", isHuazhuhui);

            return response;

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "处理失败: " + e.getMessage());
            response.put("stackTrace", e.toString());
            return response;
        }
    }

    /**
     * 计算报销金额并返回完整的结果（包含计算过程）
     */
    private ReimbursementResult calculateReimbursement(Map<String, Integer> standards,
                                                       int days, boolean hasVatInvoice,
                                                       double totalVatAmount, double totalTransportAmount,
                                                       double totalMealAmount, double totalTaxiAmount,
                                                       double totalActual, JSONObject standardInfo,
                                                       JSONArray calculationProcess,
                                                       int numberOfTravelers, String isHuazhuhui) throws JSONException {

        double totalExpenseAmount = 0.0;

        // 检查是否使用华住会
        boolean useHuazhuhui = HUAZHUHUI_YES.equals(isHuazhuhui);

        if (standards != null) {
            // 从数据库获取的各项标准金额
            int accomStandard = standards.get("accommodationStandard");  // 住宿标准（元/晚）
            int livingStandard = standards.get("livingAllowance");       // 生活补贴标准（元/天）
            int transportStandard = standards.get("transportAllowance"); // 交通补贴标准（元/天）

            // 住宿晚数 = 出差天数 - 1
            int nights = Math.max(0, days - 1); // 确保不会出现负数

            // 根据出差人数调整住宿标准 - 单人全额，多人按人均计算
            double perPersonAccomStandard = accomStandard;

            // 住宿标准总额 - 考虑人数因素，仅当有发票时才计算，否则为0
            double accomStandardTotal = hasVatInvoice ? perPersonAccomStandard * nights * numberOfTravelers : 0;

            // 计算总补贴标准 - 这是理论最大值，后面会根据实际情况调整
            double totalLivingAllowance = numberOfTravelers * days * livingStandard;
            double totalTransportAllowance = numberOfTravelers * days * transportStandard;

            // 处理餐费报销逻辑 - 使用统一的超标计算方法
            ReimbursementCalculator.OverBudgetResult mealResult =
                ReimbursementCalculator.calculateOverBudgetReimbursement(
                    totalMealAmount, totalLivingAllowance, "餐费", "生活补贴标准");

            double reimbursableMealAmount = mealResult.reimbursableAmount;
            double mealPersonalExpense = mealResult.personalExpense;
            JSONObject mealCalculationData = mealResult.calculationData;

            // 处理打车费报销逻辑 - 使用统一的超标计算方法
            ReimbursementCalculator.OverBudgetResult taxiResult =
                ReimbursementCalculator.calculateOverBudgetReimbursement(
                    totalTaxiAmount, totalTransportAllowance, "打车费", "交通补贴标准");

            double reimbursableTaxiAmount = taxiResult.reimbursableAmount;
            double taxiPersonalExpense = taxiResult.personalExpense;
            JSONObject taxiCalculationData = taxiResult.calculationData;

            // 计算实际可报销的补贴总额
            double actualAllowanceTotal = reimbursableMealAmount + reimbursableTaxiAmount;

            // 添加餐费和打车费计算过程到calculationProcess
            calculationProcess.put(new JSONObject()
                    .put("step", "餐费报销计算")
                    .put("description", "餐费与生活补贴标准比较及超标处理")
                    .put("data", mealCalculationData)
            );

            calculationProcess.put(new JSONObject()
                    .put("step", "打车费报销计算")
                    .put("description", "打车费与交通补贴标准比较及超标处理")
                    .put("data", taxiCalculationData)
            );

            // 使用ReimbursementCalculator添加补贴报销汇总
            ReimbursementCalculator.addAllowanceSummaryToCalculationProcess(
                calculationProcess, reimbursableMealAmount, reimbursableTaxiAmount,
                mealPersonalExpense, taxiPersonalExpense, actualAllowanceTotal);

            // 实际报销的住宿费用
            double reimburseAccomAmount;

            // 使用ReimbursementCalculator添加基本信息
            ReimbursementCalculator.addBasicInfoToCalculationProcess(
                calculationProcess, days, nights, numberOfTravelers, useHuazhuhui,
                accomStandard, livingStandard, transportStandard,
                accomStandardTotal, actualAllowanceTotal);

            // 使用ReimbursementCalculator添加实际费用信息
            ReimbursementCalculator.addActualExpenseToCalculationProcess(
                calculationProcess, totalTransportAmount, totalVatAmount,
                totalMealAmount, totalTaxiAmount, totalActual);

            // 计算修改后的实际费用总额 - 将餐费和打车费替换为可报销金额
            double adjustedActual = totalTransportAmount + totalVatAmount + reimbursableMealAmount + reimbursableTaxiAmount;

            // 华住会酒店全额报销住宿费用，不考虑标准限制
            if (useHuazhuhui && hasVatInvoice) {
                // 华住会酒店全额报销住宿费用
                reimburseAccomAmount = totalVatAmount;

                // 修改可报销金额计算 - 不再加补贴，因为已经包含在adjustedActual中
                totalExpenseAmount = adjustedActual;

                calculationProcess.put(new JSONObject()
                        .put("step", "华住会酒店住宿")
                        .put("description", "华住会酒店住宿费用全额报销")
                        .put("data", new JSONObject()
                                .put("可报销住宿费用", String.format("%.2f元", reimburseAccomAmount))
                                .put("特殊政策", "根据公司政策，华住会酒店住宿费用全额报销，不受标准限制")
                                .put("计算公式", "交通票金额 + 住宿费用 + 可报销餐费 + 可报销打车费")
                                .put("计算明细", String.format("%.2f + %.2f + %.2f + %.2f = %.2f元",
                                        totalTransportAmount, reimburseAccomAmount, reimbursableMealAmount, reimbursableTaxiAmount, totalExpenseAmount))
                        )
                );
            }
            // 非华住会酒店，执行常规报销逻辑
            else if (!hasVatInvoice) {
                // 无发票情况：住宿费用为0
                reimburseAccomAmount = 0;
                // 修改可报销金额计算
                totalExpenseAmount = adjustedActual;

                calculationProcess.put(new JSONObject()
                        .put("step", "无住宿发票")
                        .put("description", "无住宿发票情况下的计算")
                        .put("data", new JSONObject()
                                .put("可报销住宿费用", "0.00元")
                                .put("计算公式", "交通票金额 + 可报销餐费 + 可报销打车费")
                                .put("计算明细", String.format("%.2f + %.2f + %.2f = %.2f元",
                                        totalTransportAmount, reimbursableMealAmount, reimbursableTaxiAmount, totalExpenseAmount))
                        )
                );
            } else if (totalVatAmount <= accomStandardTotal) {
                // 有发票且未超支：以实际住宿费用为准
                reimburseAccomAmount = totalVatAmount;
                // 修改可报销金额计算
                totalExpenseAmount = adjustedActual;

                calculationProcess.put(new JSONObject()
                        .put("step", "住宿费用未超标")
                        .put("description", "住宿费用未超过标准")
                        .put("data", new JSONObject()
                                .put("可报销住宿费用", String.format("%.2f元", reimburseAccomAmount))
                                .put("计算公式", "交通票金额 + 住宿费用 + 可报销餐费 + 可报销打车费")
                                .put("计算明细", String.format("%.2f + %.2f + %.2f + %.2f = %.2f元",
                                        totalTransportAmount, reimburseAccomAmount, reimbursableMealAmount, reimbursableTaxiAmount, totalExpenseAmount))
                        )
                );
            } else {
                // 使用ReimbursementCalculator处理住宿费用超标
                reimburseAccomAmount = ReimbursementCalculator.calculateAccommodationOverBudget(
                    totalVatAmount, accomStandardTotal, calculationProcess);

                // 计算最终报销总额 - 修改为使用可报销的餐费和打车费
                totalExpenseAmount = totalTransportAmount + reimburseAccomAmount + reimbursableMealAmount + reimbursableTaxiAmount;

                // 使用ReimbursementCalculator添加最终计算过程
                ReimbursementCalculator.addFinalCalculationToCalculationProcess(
                    calculationProcess, totalTransportAmount, reimburseAccomAmount,
                    reimbursableMealAmount, reimbursableTaxiAmount, totalExpenseAmount);
            }

            // 构建标准信息对象 - 加入人数和华住会信息
            standardInfo.put("accommodationStandard", accomStandard);
            standardInfo.put("livingAllowance", livingStandard);
            standardInfo.put("transportAllowance", transportStandard);
            standardInfo.put("accomStandardTotal", accomStandardTotal);
            // 修改使用实际可报销补贴
            standardInfo.put("allowanceTotal", actualAllowanceTotal);
            standardInfo.put("hasVatInvoice", hasVatInvoice);
            standardInfo.put("days", days);
            standardInfo.put("nights", nights);
            standardInfo.put("numberOfTravelers", numberOfTravelers);
            standardInfo.put("isHuazhuhui", useHuazhuhui);

            // 新增：记录餐费和打车费对比结果
            standardInfo.put("totalMealAmount", totalMealAmount);
            standardInfo.put("totalLivingAllowance", totalLivingAllowance);
            standardInfo.put("reimbursableMealAmount", reimbursableMealAmount);
            standardInfo.put("totalTaxiAmount", totalTaxiAmount);
            standardInfo.put("totalTransportAllowance", totalTransportAllowance);
            standardInfo.put("reimbursableTaxiAmount", reimbursableTaxiAmount);

            // 华住会特殊处理的信息
            if (useHuazhuhui && hasVatInvoice) {
                standardInfo.put("huazhuhuiFullReimbursement", true);
                standardInfo.put("reimburseAccomAmount", totalVatAmount);
            } else {
                standardInfo.put("reimburseAccomAmount",
                        hasVatInvoice ? (useHuazhuhui ? totalVatAmount :
                                (totalVatAmount <= accomStandardTotal ? totalVatAmount :
                                        (totalVatAmount - accomStandardTotal) / accomStandardTotal <= 0.2 ?
                                                totalVatAmount - (totalVatAmount - accomStandardTotal) * 0.5 :
                                                accomStandardTotal)) : 0);
            }

        } else {
            // 对餐费和打车费也不做补贴比较，直接全额报销
            totalExpenseAmount = totalActual;
            standardInfo.put("message", "未找到差旅标准，按实际费用报销");
            standardInfo.put("numberOfTravelers", numberOfTravelers);
            standardInfo.put("isHuazhuhui", useHuazhuhui);

            calculationProcess.put(new JSONObject()
                    .put("step", "无差旅标准")
                    .put("description", "未找到对应的差旅标准")
                    .put("data", new JSONObject()
                            .put("出差人数", numberOfTravelers + "人")
                            .put("华住会酒店", useHuazhuhui ? "是" : "否")
                            .put("计算方式", "按实际票据费用报销")
                            .put("实际费用总计", String.format("%.2f元", totalActual))
                    )
            );
        }

        // 使用ReimbursementCalculator添加人均费用计算（如果是多人出差）
        ReimbursementCalculator.addPerPersonCalculationToCalculationProcess(
            calculationProcess, totalExpenseAmount, numberOfTravelers);

        // 使用ReimbursementCalculator添加最终报销结果
        ReimbursementCalculator.addFinalResultToCalculationProcess(
            calculationProcess, totalExpenseAmount, numberOfTravelers, useHuazhuhui);

        // 返回包含所有计算结果的ReimbursementResult对象
        return new ReimbursementResult(totalExpenseAmount, standardInfo, calculationProcess);
    }

    /**
     * 处理交通票 - 提取相关信息并添加到结果集
     */
    private double processTransportTicket(com.alibaba.fastjson.JSONObject result, String type,
                                          JSONArray transportTickets, double totalTransportAmount) throws JSONException {

        TicketProcessor processor = new TicketProcessor(result, type);
        JSONObject ticket = processor.processTransportTicket();

        // 累加交通票金额
        String amount = ticket.optString("amount", "0");
        totalTransportAmount = InvoiceUtil.addNumericAmount(totalTransportAmount, amount);

        // 添加到交通票结果集
        transportTickets.put(ticket);

        return totalTransportAmount;
    }

    /**
     * 处理打车票据 - 提取相关信息并添加到结果集
     */
    private double processTaxiTicket(com.alibaba.fastjson.JSONObject result, String type,
                                     JSONArray taxiTickets, double totalTaxiAmount) throws JSONException {

        TicketProcessor processor = new TicketProcessor(result, type);
        JSONObject taxiData = processor.processTaxiTicket();

        // 累加打车票金额
        String amount = taxiData.optString("amount", "0");
        totalTaxiAmount = InvoiceUtil.addNumericAmount(totalTaxiAmount, amount);

        // 添加到打车票结果集
        taxiTickets.put(taxiData);

        return totalTaxiAmount;
    }

    /**
     * 处理餐费票据 - 提取相关信息并添加到结果集
     */
    private double processMealTicket(com.alibaba.fastjson.JSONObject result, String type,
                                     JSONArray mealTickets, double totalMealAmount) throws JSONException {

        TicketProcessor processor = new TicketProcessor(result, type);
        JSONObject mealData = processor.processMealTicket();

        // 累加餐费票据金额
        String amount = mealData.optString("amount", "0");
        totalMealAmount = InvoiceUtil.addNumericAmount(totalMealAmount, amount);

        // 添加到餐费票据结果集
        mealTickets.put(mealData);

        return totalMealAmount;
    }

    /**
     * 处理增值税发票 - 提取相关信息并添加到结果集
     */
    private double processVatTicket(com.alibaba.fastjson.JSONObject result, String type,
                                    JSONArray vatReceipts, double totalVatAmount) throws JSONException {

        TicketProcessor processor = new TicketProcessor(result, type);
        JSONObject vatData = processor.processVatTicket();

        // 累加增值税发票金额
        String amount = vatData.optString("amount", "0");
        totalVatAmount = InvoiceUtil.addNumericAmount(totalVatAmount, amount);

        // 添加到增值税发票结果集
        vatReceipts.put(vatData);

        return totalVatAmount;
    }

    /**
     * 获取差旅标准
     */
    private Map<String, Integer> getTravelStandard(String city, String positionCode) {
        if (InvoiceUtil.isEmpty(city) || InvoiceUtil.isEmpty(positionCode)) {
            return null;
        }

        String positionPrefix = InvoiceUtil.getPositionPrefix(positionCode);
        if (positionPrefix == null) {
            return null;
        }

        RecordSet rs = new RecordSet();
        Map<String, Integer> standards = null;

        try {
            // 匹配城市
            String sql = "SELECT " +
                    positionPrefix + "zsbx AS accom_standard, " +
                    positionPrefix + "shbz AS living_allowance, " +
                    positionPrefix + "jtbz AS transport_allowance " +
                    "FROM uf_travel_standards WHERE cs = ?";

            rs.executeQuery(sql, city);

            if (rs.next()) {
                standards = new HashMap<>();
                standards.put("accommodationStandard", rs.getInt("accom_standard"));
                standards.put("livingAllowance", rs.getInt("living_allowance"));
                standards.put("transportAllowance", rs.getInt("transport_allowance"));
                return standards;
            }

            // 匹配省份
            sql = "SELECT sf FROM uf_travel_standards WHERE cs = ?";
            rs.executeQuery(sql, city);

            String province = null;
            if (rs.next()) {
                province = rs.getString("sf");
            }

            if (!InvoiceUtil.isEmpty(province)) {
                sql = "SELECT " +
                        positionPrefix + "zsbx AS accom_standard, " +
                        positionPrefix + "shbz AS living_allowance, " +
                        positionPrefix + "jtbz AS transport_allowance " +
                        "FROM uf_travel_standards WHERE sf = ? AND (cs IS NULL OR cs = '')";

                rs.executeQuery(sql, province);

                if (rs.next()) {
                    standards = new HashMap<>();
                    standards.put("accommodationStandard", rs.getInt("accom_standard"));
                    standards.put("livingAllowance", rs.getInt("living_allowance"));
                    standards.put("transportAllowance", rs.getInt("transport_allowance"));
                    return standards;
                }
            }

            // 最后尝试获取默认标准
            sql = "SELECT " +
                    positionPrefix + "zsbx AS accom_standard, " +
                    positionPrefix + "shbz AS living_allowance, " +
                    positionPrefix + "jtbz AS transport_allowance " +
                    "FROM uf_travel_standards WHERE (sf IS NULL OR sf = '') AND (cs IS NULL OR cs = '') " +
                    "AND (qy IS NOT NULL AND qy != '') LIMIT 1";

            rs.execute(sql);

            if (rs.next()) {
                standards = new HashMap<>();
                standards.put("accommodationStandard", rs.getInt("accom_standard"));
                standards.put("livingAllowance", rs.getInt("living_allowance"));
                standards.put("transportAllowance", rs.getInt("transport_allowance"));
                return standards;
            }

        } catch (Exception e) {
        }

        return null;
    }
}