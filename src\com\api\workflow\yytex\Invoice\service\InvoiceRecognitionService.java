package com.api.workflow.yytex.Invoice.service;

import com.api.workflow.yytex.Invoice.constants.ErrorMessages;
import com.api.workflow.yytex.Invoice.constants.ReimbursementResult;
import com.api.workflow.yytex.Invoice.constants.ServiceTypeConstants;
import com.api.workflow.yytex.Invoice.constants.TicketTypeConstants;
import com.api.workflow.yytex.Invoice.util.InvoiceUtil;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import weaver.conn.RecordSet;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.api.workflow.yytex.Invoice.constants.TicketTypeConstants.VAT_TYPES;

/**
 * 发票识别服务类 - 处理核心业务逻辑
 */
public class InvoiceRecognitionService {
    // 常量定义
    private final String SERVER_IP;
    private final String TICKET_API_URL;

    // 华住会常量
    private static final String HUAZHUHUI_YES = "0";

    // 发票验证器
    private InvoiceValidator invoiceValidator = new InvoiceValidator();

    /**
     * 构造函数 - 接收服务器配置
     */
    public InvoiceRecognitionService(String serverIp, String ticketApiUrl) {
        this.SERVER_IP = serverIp;
        this.TICKET_API_URL = ticketApiUrl;
    }

    public JSONObject processAttachments(
            String transportAttachIds, String vatAttachIds,
            String mealAttachIds, String taxiAttachIds,
            String positionCode, int days, String city, String requestid,
            int numberOfTravelers, String isHuazhuhui) throws JSONException {
        try {
            // 校验至少有一种票据
            if (InvoiceUtil.isEmpty(transportAttachIds) &&
                    InvoiceUtil.isEmpty(vatAttachIds) &&
                    InvoiceUtil.isEmpty(mealAttachIds) &&
                    InvoiceUtil.isEmpty(taxiAttachIds)) {
                JSONObject response = new JSONObject();
                response.put("success", false);
                response.put("message", ErrorMessages.NO_ATTACHMENTS);
                return response;
            }

            // 调用处理方法，传递所有参数
            JSONObject result = processAllAttachmentIds(transportAttachIds, vatAttachIds,
                    mealAttachIds, taxiAttachIds,
                    positionCode, days, city, requestid,
                    numberOfTravelers, isHuazhuhui);

            return result;

        } catch (Exception e) {
            JSONObject response = new JSONObject();
            response.put("success", false);
            response.put("message", "处理失败: " + e.getMessage());
            return response;
        }
    }

    //处理所有附件ID
    private JSONObject processAllAttachmentIds(String transportAttachIds, String vatAttachIds,
                                               String mealAttachIds, String taxiAttachIds,
                                               String positionCode, int days, String city,
                                               String requestid, int numberOfTravelers, String isHuazhuhui) throws JSONException {
        JSONObject response = new JSONObject();
        RecordSet rs = new RecordSet();

        try {
            // 处理交通票附件
            List<Integer> transportFileIds = InvoiceUtil.getAttachmentIds(rs, transportAttachIds);
            List<String> transportFileUrls = InvoiceUtil.getFileDownUrls(transportFileIds, SERVER_IP);

            // 处理发票附件
            List<Integer> vatFileIds = InvoiceUtil.getAttachmentIds(rs, vatAttachIds);
            List<String> vatFileUrls = InvoiceUtil.getFileDownUrls(vatFileIds, SERVER_IP);

            // 处理餐费票据附件
            List<Integer> mealFileIds = InvoiceUtil.getAttachmentIds(rs, mealAttachIds);
            List<String> mealFileUrls = InvoiceUtil.getFileDownUrls(mealFileIds, SERVER_IP);

            // 处理打车票据附件
            List<Integer> taxiFileIds = InvoiceUtil.getAttachmentIds(rs, taxiAttachIds);
            List<String> taxiFileUrls = InvoiceUtil.getFileDownUrls(taxiFileIds, SERVER_IP);

            if (transportFileUrls.isEmpty() && vatFileUrls.isEmpty() &&
                    mealFileUrls.isEmpty() && taxiFileUrls.isEmpty()) {
                response.put("success", false);
                response.put("message", ErrorMessages.NO_VALID_FILES);
                return response;
            }

            // 使用InvoiceUtil构建请求JSON
            String jsonBody = InvoiceUtil.buildTicketJsonRequestBody(
                    transportFileUrls, vatFileUrls, mealFileUrls, taxiFileUrls);

            // 发送识别请求
            weaverjn.util.TicketHttpClient httpClient = new weaverjn.util.TicketHttpClient();
            String recognitionResponse = httpClient.sendTicketData(TICKET_API_URL, jsonBody);

            if (InvoiceUtil.isEmpty(recognitionResponse)) {
                response.put("success", false);
                response.put("message", ErrorMessages.NO_RESPONSE);
                return response;
            }

            // 解析响应
            com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(recognitionResponse);

            if (!jsonObject.getBooleanValue("success")) {
                response.put("success", false);
                response.put("message", jsonObject.getString("message"));
                return response;
            }

            // 提取响应数据
            com.alibaba.fastjson.JSONObject data = jsonObject.getJSONObject("data");

            // 处理票据数据
            double totalTransportAmount = 0.0;
            double totalVatAmount = 0.0;
            double totalMealAmount = 0.0;
            double totalTaxiAmount = 0.0;

            JSONArray transportTickets = new JSONArray();
            JSONArray vatReceipts = new JSONArray();
            JSONArray mealTickets = new JSONArray();
            JSONArray taxiTickets = new JSONArray();

            // 用于收集所有发票号码
            List<String> allInvoiceNumbers = new ArrayList<>();

            // 处理增值税发票数据
            if (data.containsKey("vats")) {
                com.alibaba.fastjson.JSONArray vats = data.getJSONArray("vats");
                if (vats != null && !vats.isEmpty()) {
                    for (int i = 0; i < vats.size(); i++) {
                        com.alibaba.fastjson.JSONObject vat = vats.getJSONObject(i);

                        // 创建发票数据
                        JSONObject vatData = new JSONObject();
                        vatData.put("ticketType", "vat_invoice");
                        vatData.put("purchaserName", vat.getString("purchaserName"));
                        vatData.put("purchaserRegisterNum", vat.getString("purchaserRegisterNum"));

                        // 处理项目名称列表
                        String commodityNames = "";
                        com.alibaba.fastjson.JSONArray commodityArray = vat.getJSONArray("commodityName");
                        if (commodityArray != null && !commodityArray.isEmpty()) {
                            commodityNames = InvoiceUtil.extractArrayWords(commodityArray);
                        }
                        vatData.put("commodityName", commodityNames);

                        // 获取发票金额
                        String amountStr = vat.getString("amountInFiguers");
                        vatData.put("amount", amountStr);

                        // 累加发票金额
                        totalVatAmount = InvoiceUtil.addNumericAmount(totalVatAmount, amountStr);

                        // 获取发票号并保存
                        String invoiceNum = vat.getString("invoiceNum");
                        vatData.put("invoiceNum", invoiceNum);

                        // 添加发票号到列表中
                        if (!InvoiceUtil.isEmpty(invoiceNum)) {
                            allInvoiceNumbers.add(invoiceNum);
                        }

                        vatData.put("invoiceDate", vat.getString("invoiceDate"));
                        vatData.put("sellerName", vat.getString("sellerName"));

                        // 添加到结果集
                        vatReceipts.put(vatData);
                    }
                }
            }

            // 处理multiples数组中的结果 - 根据不同type和ServiceType分类
            if (data.containsKey("multiples")) {
                com.alibaba.fastjson.JSONArray multiples = data.getJSONArray("multiples");
                if (multiples != null && !multiples.isEmpty()) {
                    for (int i = 0; i < multiples.size(); i++) {
                        com.alibaba.fastjson.JSONObject multipleItem = multiples.getJSONObject(i);
                        String type = multipleItem.getString("type");
                        com.alibaba.fastjson.JSONObject result = multipleItem.getJSONObject("result");

                        // 检查ServiceType字段
                        String serviceType = InvoiceUtil.extractFirstWord(result, "ServiceType");

                        // 检查InvoiceTag字段，用于识别客运服务类发票
                        String invoiceTag = InvoiceUtil.extractFirstWord(result, "InvoiceTag");

                        // 检查是否为交通运输服务
                        boolean isTransportService = InvoiceUtil.isCommodityTransportService(result);

                        // 检查是否为租车服务
                        boolean isRentalCarService = InvoiceUtil.isCommodityRentalCarService(result);

                        // 提取并保存发票号 - 收集所有可能包含发票号的字段
                        String invoiceNum = "";

                        if (result.containsKey("InvoiceNum")) {
                            invoiceNum = InvoiceUtil.extractFirstWord(result, "InvoiceNum");
                        } else if (result.containsKey("invoice_num")) {
                            invoiceNum = InvoiceUtil.extractFirstWord(result, "invoice_num");
                        } else if (result.containsKey("ticket_number")) {
                            invoiceNum = InvoiceUtil.extractFirstWord(result, "ticket_number");
                        }

                        // 如果发票号不为空，添加到列表
                        if (!InvoiceUtil.isEmpty(invoiceNum)) {
                            allInvoiceNumbers.add(invoiceNum);
                        }

                        // 根据类型和服务类型分类处理
                        if (VAT_TYPES.contains(type) && ServiceTypeConstants.DINING.equals(serviceType)) {
                            // 处理餐饮类增值税发票为餐费票据
                            totalMealAmount = processMealTicket(result, type, mealTickets, totalMealAmount);
                        }
                        else if (VAT_TYPES.contains(type) && isRentalCarService) {
                            // 处理租车服务类增值税发票为交通票据
                            totalTransportAmount = processTransportTicket(result, type, transportTickets, totalTransportAmount);
                        }
                        else if (VAT_TYPES.contains(type) &&
                                (ServiceTypeConstants.TRANSPORT.equals(serviceType) ||
                                        ServiceTypeConstants.PASSENGER_TRANSPORT.equals(invoiceTag) ||
                                        isTransportService)) {
                            // 处理交通/客运服务类增值税发票为打车票据
                            totalTaxiAmount = processTaxiTicket(result, type, taxiTickets, totalTaxiAmount);
                        }
                        else if (TicketTypeConstants.TRANSPORT_TYPES.contains(type)) {
                            totalTransportAmount = processTransportTicket(result, type, transportTickets, totalTransportAmount);
                        }
                        else if (TicketTypeConstants.TAXI_TYPES.contains(type)) {
                            totalTaxiAmount = processTaxiTicket(result, type, taxiTickets, totalTaxiAmount);
                        }
                        else if (TicketTypeConstants.MEAL_TYPES.contains(type)) {
                            totalMealAmount = processMealTicket(result, type, mealTickets, totalMealAmount);
                        }
                        else if (VAT_TYPES.contains(type)) {
                            totalVatAmount = processVatTicket(result, type, vatReceipts, totalVatAmount);
                        }
                        else if ("others".equals(type)) {
                            // 对于其他类型，先检查是否为租车服务
                            if (isRentalCarService) {
                                // 租车服务归类为交通票
                                totalTransportAmount = processTransportTicket(result, type, transportTickets, totalTransportAmount);
                            }
                            // 然后尝试通过ServiceType判断
                            else if (ServiceTypeConstants.DINING.equals(serviceType)) {
                                totalMealAmount = processMealTicket(result, type, mealTickets, totalMealAmount);
                            } else if (ServiceTypeConstants.TRANSPORT.equals(serviceType)) {
                                totalTaxiAmount = processTaxiTicket(result, type, taxiTickets, totalTaxiAmount);
                            } else {
                                // 默认放入餐费票据
                                totalMealAmount = processMealTicket(result, type, mealTickets, totalMealAmount);
                            }
                        }
                    }
                }
            }

            // 检查发票号是否重复 - 包括当前表单内的重复和数据库中的重复
            if (!allInvoiceNumbers.isEmpty()) {
                // 调用发票验证器进行检查
                JSONObject checkResult = invoiceValidator.checkDuplicateInvoices(allInvoiceNumbers, requestid);

                // 如果检查结果不是成功，返回错误信息
                if (!checkResult.getBoolean("success")) {
                    return checkResult;
                }
            }

            // 判断是否有各类发票
            boolean hasVatInvoice = !InvoiceUtil.isEmpty(vatAttachIds);

            // 计算报销金额
            double totalActual = totalTransportAmount + totalVatAmount + totalMealAmount + totalTaxiAmount;
            Map<String, Integer> standards = getTravelStandard(city, positionCode);

            // 计算最终报销金额及附加详情
            JSONObject standardInfo = new JSONObject();
            JSONArray calculationProcess = new JSONArray();

            // 使用新的方法计算报销金额和获取计算过程，传入新参数
            ReimbursementResult reimbursementResult = calculateReimbursement(
                    standards, days, hasVatInvoice,
                    totalVatAmount, totalTransportAmount, totalMealAmount, totalTaxiAmount,
                    totalActual, standardInfo, calculationProcess,
                    numberOfTravelers, isHuazhuhui);

            // 构建最终响应 - 包含计算过程和新参数信息
            response.put("success", true);
            response.put("transportTickets", transportTickets);
            response.put("vatReceipts", vatReceipts);
            response.put("mealTickets", mealTickets);
            response.put("taxiTickets", taxiTickets);
            response.put("totalTransportAmount", totalTransportAmount);
            response.put("totalVatAmount", totalVatAmount);
            response.put("totalMealAmount", totalMealAmount);
            response.put("totalTaxiAmount", totalTaxiAmount);
            response.put("totalActualAmount", totalActual);
            response.put("totalExpenseAmount", reimbursementResult.totalExpenseAmount);
            response.put("standardInfo", reimbursementResult.standardInfo);
            response.put("calculationProcess", reimbursementResult.calculationProcess);
            response.put("destination", city);
            response.put("position", InvoiceUtil.mapPositionToName(positionCode));
            response.put("numberOfTravelers", numberOfTravelers);
            response.put("isHuazhuhui", isHuazhuhui);

            return response;

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "处理失败: " + e.getMessage());
            response.put("stackTrace", e.toString());
            return response;
        }
    }

    /**
     * 计算报销金额并返回完整的结果（包含计算过程）
     */
    private ReimbursementResult calculateReimbursement(Map<String, Integer> standards,
                                                       int days, boolean hasVatInvoice,
                                                       double totalVatAmount, double totalTransportAmount,
                                                       double totalMealAmount, double totalTaxiAmount,
                                                       double totalActual, JSONObject standardInfo,
                                                       JSONArray calculationProcess,
                                                       int numberOfTravelers, String isHuazhuhui) throws JSONException {

        double totalExpenseAmount = 0.0;

        // 检查是否使用华住会
        boolean useHuazhuhui = HUAZHUHUI_YES.equals(isHuazhuhui);

        if (standards != null) {
            // 从数据库获取的各项标准金额
            int accomStandard = standards.get("accommodationStandard");  // 住宿标准（元/晚）
            int livingStandard = standards.get("livingAllowance");       // 生活补贴标准（元/天）
            int transportStandard = standards.get("transportAllowance"); // 交通补贴标准（元/天）

            // 住宿晚数 = 出差天数 - 1
            int nights = Math.max(0, days - 1); // 确保不会出现负数

            // 根据出差人数调整住宿标准 - 单人全额，多人按人均计算
            double perPersonAccomStandard = accomStandard;

            // 住宿标准总额 - 考虑人数因素，仅当有发票时才计算，否则为0
            double accomStandardTotal = hasVatInvoice ? perPersonAccomStandard * nights * numberOfTravelers : 0;

            // 计算总补贴标准 - 这是理论最大值，后面会根据实际情况调整
            double totalLivingAllowance = numberOfTravelers * days * livingStandard;
            double totalTransportAllowance = numberOfTravelers * days * transportStandard;

            // 处理餐费报销逻辑
            double reimbursableMealAmount;
            double mealPersonalExpense = 0;
            JSONObject mealCalculationData = new JSONObject();

            if (totalMealAmount <= totalLivingAllowance) {
                // 餐费未超标，全额报销
                reimbursableMealAmount = totalMealAmount;
                mealCalculationData.put("计算方式", "餐费未超标，全额报销")
                        .put("餐费金额", String.format("%.2f元", totalMealAmount))
                        .put("生活补贴标准", String.format("%.2f元", totalLivingAllowance))
                        .put("可报销餐费金额", String.format("%.2f元", reimbursableMealAmount));
            } else {
                // 餐费超标，计算超出部分
                double mealOverBudget = totalMealAmount - totalLivingAllowance;
                double mealOverBudgetRatio = mealOverBudget / totalLivingAllowance;

                if (mealOverBudgetRatio <= 0.2) {
                    // 超支20%以内，个人承担超额部分的50%
                    mealPersonalExpense = mealOverBudget * 0.5;
                    reimbursableMealAmount = totalMealAmount - mealPersonalExpense;
                    mealCalculationData.put("计算方式", "餐费超标20%以内，个人承担超额部分的50%")
                            .put("餐费金额", String.format("%.2f元", totalMealAmount))
                            .put("生活补贴标准", String.format("%.2f元", totalLivingAllowance))
                            .put("超出金额", String.format("%.2f元", mealOverBudget))
                            .put("超出比例", String.format("%.2f%%", mealOverBudgetRatio * 100))
                            .put("个人承担金额", String.format("%.2f元", mealPersonalExpense))
                            .put("可报销餐费金额", String.format("%.2f元", reimbursableMealAmount));
                } else {
                    // 超支20%以上，仅报销标准额度
                    mealPersonalExpense = mealOverBudget;
                    reimbursableMealAmount = totalLivingAllowance;
                    mealCalculationData.put("计算方式", "餐费超标20%以上，仅报销标准额度")
                            .put("餐费金额", String.format("%.2f元", totalMealAmount))
                            .put("生活补贴标准", String.format("%.2f元", totalLivingAllowance))
                            .put("超出金额", String.format("%.2f元", mealOverBudget))
                            .put("超出比例", String.format("%.2f%%", mealOverBudgetRatio * 100))
                            .put("个人承担金额", String.format("%.2f元", mealPersonalExpense))
                            .put("可报销餐费金额", String.format("%.2f元", reimbursableMealAmount));
                }
            }

            // 处理打车费报销逻辑
            double reimbursableTaxiAmount;
            double taxiPersonalExpense = 0;
            JSONObject taxiCalculationData = new JSONObject();

            if (totalTaxiAmount <= totalTransportAllowance) {
                // 打车费未超标，全额报销
                reimbursableTaxiAmount = totalTaxiAmount;
                taxiCalculationData.put("计算方式", "打车费未超标，全额报销")
                        .put("打车费金额", String.format("%.2f元", totalTaxiAmount))
                        .put("交通补贴标准", String.format("%.2f元", totalTransportAllowance))
                        .put("可报销打车费金额", String.format("%.2f元", reimbursableTaxiAmount));
            } else {
                // 打车费超标，计算超出部分
                double taxiOverBudget = totalTaxiAmount - totalTransportAllowance;
                double taxiOverBudgetRatio = taxiOverBudget / totalTransportAllowance;

                if (taxiOverBudgetRatio <= 0.2) {
                    // 超支20%以内，个人承担超额部分的50%
                    taxiPersonalExpense = taxiOverBudget * 0.5;
                    reimbursableTaxiAmount = totalTaxiAmount - taxiPersonalExpense;
                    taxiCalculationData.put("计算方式", "打车费超标20%以内，个人承担超额部分的50%")
                            .put("打车费金额", String.format("%.2f元", totalTaxiAmount))
                            .put("交通补贴标准", String.format("%.2f元", totalTransportAllowance))
                            .put("超出金额", String.format("%.2f元", taxiOverBudget))
                            .put("超出比例", String.format("%.2f%%", taxiOverBudgetRatio * 100))
                            .put("个人承担金额", String.format("%.2f元", taxiPersonalExpense))
                            .put("可报销打车费金额", String.format("%.2f元", reimbursableTaxiAmount));
                } else {
                    // 超支20%以上，仅报销标准额度
                    taxiPersonalExpense = taxiOverBudget;
                    reimbursableTaxiAmount = totalTransportAllowance;
                    taxiCalculationData.put("计算方式", "打车费超标20%以上，仅报销标准额度")
                            .put("打车费金额", String.format("%.2f元", totalTaxiAmount))
                            .put("交通补贴标准", String.format("%.2f元", totalTransportAllowance))
                            .put("超出金额", String.format("%.2f元", taxiOverBudget))
                            .put("超出比例", String.format("%.2f%%", taxiOverBudgetRatio * 100))
                            .put("个人承担金额", String.format("%.2f元", taxiPersonalExpense))
                            .put("可报销打车费金额", String.format("%.2f元", reimbursableTaxiAmount));
                }
            }

            // 计算实际可报销的补贴总额
            double actualAllowanceTotal = reimbursableMealAmount + reimbursableTaxiAmount;

            // 添加餐费和打车费计算过程到calculationProcess
            calculationProcess.put(new JSONObject()
                    .put("step", "餐费报销计算")
                    .put("description", "餐费与生活补贴标准比较及超标处理")
                    .put("data", mealCalculationData)
            );

            calculationProcess.put(new JSONObject()
                    .put("step", "打车费报销计算")
                    .put("description", "打车费与交通补贴标准比较及超标处理")
                    .put("data", taxiCalculationData)
            );

            calculationProcess.put(new JSONObject()
                    .put("step", "补贴报销汇总")
                    .put("description", "餐费与打车费可报销金额汇总")
                    .put("data", new JSONObject()
                            .put("可报销餐费金额", String.format("%.2f元", reimbursableMealAmount))
                            .put("可报销打车费金额", String.format("%.2f元", reimbursableTaxiAmount))
                            .put("个人总承担金额", String.format("%.2f元", mealPersonalExpense + taxiPersonalExpense))
                            .put("实际可报销补贴总额", String.format("%.2f元", actualAllowanceTotal))
                    )
            );

            // 实际报销的住宿费用
            double reimburseAccomAmount;

            // 记录基本信息 - 包含人数和华住会信息
            JSONObject basicInfoData = new JSONObject()
                    .put("出差天数", days + "天")
                    .put("住宿晚数", nights + "晚")
                    .put("出差人数", numberOfTravelers + "人")
                    .put("华住会酒店", useHuazhuhui ? "是" : "否")
                    .put("住宿标准", accomStandard + "元/晚/人")
                    .put("生活补贴标准", livingStandard + "元/天/人")
                    .put("交通补贴标准", transportStandard + "元/天/人");

            if (useHuazhuhui) {
                basicInfoData.put("华住会政策", "住宿费用全额报销");
            }

            basicInfoData.put("住宿标准总额", String.format("%.2f元", accomStandardTotal));
            // 修改这行来使用实际可报销补贴总额
            basicInfoData.put("补贴总额", String.format("%.2f元", actualAllowanceTotal));

            calculationProcess.put(new JSONObject()
                    .put("step", "基本信息")
                    .put("description", "报销计算基础数据")
                    .put("data", basicInfoData)
            );

            // 记录实际票据金额
            calculationProcess.put(new JSONObject()
                    .put("step", "实际费用")
                    .put("description", "各类票据实际金额")
                    .put("data", new JSONObject()
                            .put("交通票金额", String.format("%.2f元", totalTransportAmount))
                            .put("住宿发票金额", String.format("%.2f元", totalVatAmount))
                            .put("餐费票据金额", String.format("%.2f元", totalMealAmount))
                            .put("打车票据金额", String.format("%.2f元", totalTaxiAmount))
                            .put("实际费用总计", String.format("%.2f元", totalActual))
                    )
            );

            // 计算修改后的实际费用总额 - 将餐费和打车费替换为可报销金额
            double adjustedActual = totalTransportAmount + totalVatAmount + reimbursableMealAmount + reimbursableTaxiAmount;

            // 华住会酒店全额报销住宿费用，不考虑标准限制
            if (useHuazhuhui && hasVatInvoice) {
                // 华住会酒店全额报销住宿费用
                reimburseAccomAmount = totalVatAmount;

                // 修改可报销金额计算 - 不再加补贴，因为已经包含在adjustedActual中
                totalExpenseAmount = adjustedActual;

                calculationProcess.put(new JSONObject()
                        .put("step", "华住会酒店住宿")
                        .put("description", "华住会酒店住宿费用全额报销")
                        .put("data", new JSONObject()
                                .put("可报销住宿费用", String.format("%.2f元", reimburseAccomAmount))
                                .put("特殊政策", "根据公司政策，华住会酒店住宿费用全额报销，不受标准限制")
                                .put("计算公式", "交通票金额 + 住宿费用 + 可报销餐费 + 可报销打车费")
                                .put("计算明细", String.format("%.2f + %.2f + %.2f + %.2f = %.2f元",
                                        totalTransportAmount, reimburseAccomAmount, reimbursableMealAmount, reimbursableTaxiAmount, totalExpenseAmount))
                        )
                );
            }
            // 非华住会酒店，执行常规报销逻辑
            else if (!hasVatInvoice) {
                // 无发票情况：住宿费用为0
                reimburseAccomAmount = 0;
                // 修改可报销金额计算
                totalExpenseAmount = adjustedActual;

                calculationProcess.put(new JSONObject()
                        .put("step", "无住宿发票")
                        .put("description", "无住宿发票情况下的计算")
                        .put("data", new JSONObject()
                                .put("可报销住宿费用", "0.00元")
                                .put("计算公式", "交通票金额 + 可报销餐费 + 可报销打车费")
                                .put("计算明细", String.format("%.2f + %.2f + %.2f = %.2f元",
                                        totalTransportAmount, reimbursableMealAmount, reimbursableTaxiAmount, totalExpenseAmount))
                        )
                );
            } else if (totalVatAmount <= accomStandardTotal) {
                // 有发票且未超支：以实际住宿费用为准
                reimburseAccomAmount = totalVatAmount;
                // 修改可报销金额计算
                totalExpenseAmount = adjustedActual;

                calculationProcess.put(new JSONObject()
                        .put("step", "住宿费用未超标")
                        .put("description", "住宿费用未超过标准")
                        .put("data", new JSONObject()
                                .put("可报销住宿费用", String.format("%.2f元", reimburseAccomAmount))
                                .put("计算公式", "交通票金额 + 住宿费用 + 可报销餐费 + 可报销打车费")
                                .put("计算明细", String.format("%.2f + %.2f + %.2f + %.2f = %.2f元",
                                        totalTransportAmount, reimburseAccomAmount, reimbursableMealAmount, reimbursableTaxiAmount, totalExpenseAmount))
                        )
                );
            } else {
                // 超出金额 = 实际住宿费用 - 住宿标准总额
                double overBudget = totalVatAmount - accomStandardTotal;
                // 超支比例 = 超出金额 / 住宿标准总额
                double overBudgetRatio = overBudget / accomStandardTotal;

                calculationProcess.put(new JSONObject()
                        .put("step", "住宿费用超标分析")
                        .put("description", "住宿费用超过标准的计算")
                        .put("data", new JSONObject()
                                .put("超出金额", String.format("%.2f元", overBudget))
                                .put("超出比例", String.format("%.2f%%", overBudgetRatio * 100))
                        )
                );

                if (overBudgetRatio <= 0.2) {
                    // 超支20%以内：个人承担超额部分的50%
                    double personalExpense = overBudget * 0.5;  // 个人承担费用
                    // 可报销的住宿费用 = 实际住宿费用 - 个人承担部分
                    reimburseAccomAmount = totalVatAmount - personalExpense;

                    calculationProcess.put(new JSONObject()
                            .put("step", "住宿费用超标处理(≤20%)")
                            .put("description", "超标20%以内，个人承担超额部分的50%")
                            .put("data", new JSONObject()
                                    .put("个人承担费用", String.format("%.2f元", personalExpense))
                                    .put("计算公式", "实际住宿费用 - 个人承担部分")
                                    .put("计算明细", String.format("%.2f - %.2f = %.2f元",
                                            totalVatAmount, personalExpense, reimburseAccomAmount))
                            )
                    );
                } else {
                    // 超支20%以上：住宿仅报销标准额度
                    reimburseAccomAmount = accomStandardTotal;

                    calculationProcess.put(new JSONObject()
                            .put("step", "住宿费用超标处理(>20%)")
                            .put("description", "超标20%以上，仅报销标准额度")
                            .put("data", new JSONObject()
                                    .put("可报销住宿费用", String.format("%.2f元", reimburseAccomAmount))
                                    .put("超额部分个人承担", String.format("%.2f元", totalVatAmount - reimburseAccomAmount))
                            )
                    );
                }

                // 计算最终报销总额 - 修改为使用可报销的餐费和打车费
                totalExpenseAmount = totalTransportAmount + reimburseAccomAmount + reimbursableMealAmount + reimbursableTaxiAmount;

                calculationProcess.put(new JSONObject()
                        .put("step", "最终报销金额计算")
                        .put("description", "汇总各项费用得出最终报销金额")
                        .put("data", new JSONObject()
                                .put("计算公式", "交通票金额 + 可报销住宿费用 + 可报销餐费 + 可报销打车费")
                                .put("计算明细", String.format("%.2f + %.2f + %.2f + %.2f = %.2f元",
                                        totalTransportAmount, reimburseAccomAmount, reimbursableMealAmount, reimbursableTaxiAmount, totalExpenseAmount))
                        )
                );
            }

            // 构建标准信息对象 - 加入人数和华住会信息
            standardInfo.put("accommodationStandard", accomStandard);
            standardInfo.put("livingAllowance", livingStandard);
            standardInfo.put("transportAllowance", transportStandard);
            standardInfo.put("accomStandardTotal", accomStandardTotal);
            // 修改使用实际可报销补贴
            standardInfo.put("allowanceTotal", actualAllowanceTotal);
            standardInfo.put("hasVatInvoice", hasVatInvoice);
            standardInfo.put("days", days);
            standardInfo.put("nights", nights);
            standardInfo.put("numberOfTravelers", numberOfTravelers);
            standardInfo.put("isHuazhuhui", useHuazhuhui);

            // 新增：记录餐费和打车费对比结果
            standardInfo.put("totalMealAmount", totalMealAmount);
            standardInfo.put("totalLivingAllowance", totalLivingAllowance);
            standardInfo.put("reimbursableMealAmount", reimbursableMealAmount);
            standardInfo.put("totalTaxiAmount", totalTaxiAmount);
            standardInfo.put("totalTransportAllowance", totalTransportAllowance);
            standardInfo.put("reimbursableTaxiAmount", reimbursableTaxiAmount);

            // 华住会特殊处理的信息
            if (useHuazhuhui && hasVatInvoice) {
                standardInfo.put("huazhuhuiFullReimbursement", true);
                standardInfo.put("reimburseAccomAmount", totalVatAmount);
            } else {
                standardInfo.put("reimburseAccomAmount",
                        hasVatInvoice ? (useHuazhuhui ? totalVatAmount :
                                (totalVatAmount <= accomStandardTotal ? totalVatAmount :
                                        (totalVatAmount - accomStandardTotal) / accomStandardTotal <= 0.2 ?
                                                totalVatAmount - (totalVatAmount - accomStandardTotal) * 0.5 :
                                                accomStandardTotal)) : 0);
            }

        } else {
            // 对餐费和打车费也不做补贴比较，直接全额报销
            totalExpenseAmount = totalActual;
            standardInfo.put("message", "未找到差旅标准，按实际费用报销");
            standardInfo.put("numberOfTravelers", numberOfTravelers);
            standardInfo.put("isHuazhuhui", useHuazhuhui);

            calculationProcess.put(new JSONObject()
                    .put("step", "无差旅标准")
                    .put("description", "未找到对应的差旅标准")
                    .put("data", new JSONObject()
                            .put("出差人数", numberOfTravelers + "人")
                            .put("华住会酒店", useHuazhuhui ? "是" : "否")
                            .put("计算方式", "按实际票据费用报销")
                            .put("实际费用总计", String.format("%.2f元", totalActual))
                    )
            );
        }

        // 如果是多人出差，添加人均信息
        if (numberOfTravelers > 1) {
            double perPersonExpense = totalExpenseAmount / numberOfTravelers;
            calculationProcess.put(new JSONObject()
                    .put("step", "人均费用计算")
                    .put("description", "计算每人平均报销金额")
                    .put("data", new JSONObject()
                            .put("总报销金额", String.format("%.2f元", totalExpenseAmount))
                            .put("出差人数", numberOfTravelers + "人")
                            .put("人均报销金额", String.format("%.2f元/人", perPersonExpense))
                    )
            );
        }

        // 最后添加最终报销金额信息
        calculationProcess.put(new JSONObject()
                .put("step", "报销结果")
                .put("description", "最终可报销金额")
                .put("data", new JSONObject()
                        .put("可报销总金额", String.format("%.2f元", totalExpenseAmount))
                        .put("出差人数", numberOfTravelers + "人")
                        .put("华住会酒店", useHuazhuhui ? "住宿费用全额报销" : "未使用")
                )
        );

        // 返回包含所有计算结果的ReimbursementResult对象
        return new ReimbursementResult(totalExpenseAmount, standardInfo, calculationProcess);
    }

    /**
     * 处理交通票 - 提取相关信息并添加到结果集
     */
    private double processTransportTicket(com.alibaba.fastjson.JSONObject result, String type,
                                          JSONArray transportTickets, double totalTransportAmount) throws JSONException {
        JSONObject ticket = new JSONObject();

        // 添加票据类型标识
        ticket.put("ticketType", type);

        // 提取共有字段
        InvoiceUtil.extractField(result, "name", ticket, "name");
        InvoiceUtil.extractDateTime(result, ticket);
        InvoiceUtil.extractField(result, "starting_station", ticket, "startStation");
        InvoiceUtil.extractField(result, "destination_station", ticket, "destStation");

        // 提取交通工具编号
        if ("train_ticket".equals(type)) {
            InvoiceUtil.extractField(result, "train_num", ticket, "transportNum");
        }
        else if ("air_ticket".equals(type)) {
            String flightInfo = InvoiceUtil.extractCarrierAndFlight(result);
            if (flightInfo != null) {
                ticket.put("transportNum", flightInfo);
            }
        }
        else if ("bus_ticket".equals(type)) {
            InvoiceUtil.extractField(result, "bus_num", ticket, "transportNum");
        }
        else if ("ferry_ticket".equals(type)) {
            InvoiceUtil.extractField(result, "ferry_num", ticket, "transportNum");
        }
        else if (VAT_TYPES.contains(type)) {
            // 检查是否为租车服务
            if (InvoiceUtil.isCommodityRentalCarService(result)) {
                // 对于经营租赁/租车服务的增值税发票，添加特殊标识
                ticket.put("transportNum", "租车服务");

                // 补充出发地和目的地（如果未提供）
                if (!ticket.has("startStation")) {
                    ticket.put("startStation", "租车地点");
                }
                if (!ticket.has("destStation")) {
                    ticket.put("destStation", "返还地点");
                }
            }
        }

        if (!ticket.has("transportNum")) {
            if (!InvoiceUtil.extractField(result, "vehicle_num", ticket, "transportNum") &&
                    !InvoiceUtil.extractField(result, "transport_num", ticket, "transportNum")) {
                ticket.put("transportNum", InvoiceUtil.formatTicketType(type));
            }
        }

        // 提取座位类型
        try {
            // 检查是否是租车服务发票
            if (VAT_TYPES.contains(type) && InvoiceUtil.isCommodityRentalCarService(result)) {
                ticket.put("seatType", "经营租赁服务");
            } else {
                String seatType = InvoiceUtil.extractSeatType(result, type);
                ticket.put("seatType", seatType);
            }
        } catch (Exception e) {
            // 座位类型提取失败，使用默认值
            ticket.put("seatType", InvoiceUtil.formatTicketType(type));
        }

        // 提取金额
        String amount = "";
        // 对于增值税发票，使用AmountInFiguers字段
        if (VAT_TYPES.contains(type) && result.containsKey("AmountInFiguers")) {
            amount = InvoiceUtil.extractFirstWord(result, "AmountInFiguers");
        } else {
            amount = InvoiceUtil.extractAmount(result);
        }
        ticket.put("amount", amount);

        // 累加交通票金额
        totalTransportAmount = InvoiceUtil.addNumericAmount(totalTransportAmount, amount);

        // 提取票号或发票号
        if (VAT_TYPES.contains(type)) {
            InvoiceUtil.extractField(result, "InvoiceNum", ticket, "invoiceNum");
            InvoiceUtil.extractField(result, "InvoiceDate", ticket, "invoiceDate");
            InvoiceUtil.extractField(result, "SellerName", ticket, "sellerName");

            // 增值税发票中提取购买方信息
            InvoiceUtil.extractField(result, "PurchaserName", ticket, "purchaserName");

            // 提取商品名称，用于显示为备注
            if (result.containsKey("CommodityName")) {
                String commodityNames = InvoiceUtil.extractCommodityNames(result, "CommodityName");
                ticket.put("commodityName", commodityNames);
            }
        } else {
            if (!InvoiceUtil.extractField(result, "ticket_number", ticket, "invoiceNum")) {
                InvoiceUtil.extractField(result, "invoice_num", ticket, "invoiceNum");
            }
        }

        // 兼容前端的字段名 - 将transportNum复制到trainNum
        if (ticket.has("transportNum")) {
            ticket.put("trainNum", ticket.getString("transportNum"));
        }

        // 添加到交通票结果集
        transportTickets.put(ticket);

        return totalTransportAmount;
    }

    /**
     * 处理打车票据 - 提取相关信息并添加到结果集
     */
    private double processTaxiTicket(com.alibaba.fastjson.JSONObject result, String type,
                                     JSONArray taxiTickets, double totalTaxiAmount) throws JSONException {
        JSONObject taxiData = new JSONObject();
        taxiData.put("ticketType", type);

        boolean isVatInvoice = VAT_TYPES.contains(type);

        if (isVatInvoice) {
            InvoiceUtil.extractField(result, "PurchaserName", taxiData, "purchaserName");
            InvoiceUtil.extractField(result, "PurchaserRegisterNum", taxiData, "purchaserTaxNum");
        } else {
            InvoiceUtil.extractField(result, "buyer", taxiData, "purchaserName");
            InvoiceUtil.extractField(result, "buyer_tax_id", taxiData, "purchaserTaxNum");
        }

        // 提取项目名称
        String itemName = "";
        if (isVatInvoice && result.containsKey("CommodityName")) {
            itemName = InvoiceUtil.extractCommodityNames(result, "CommodityName");
        } else if (result.containsKey("service")) {
            itemName = InvoiceUtil.extractFirstWord(result, "service");
        } else if (result.containsKey("item")) {
            itemName = InvoiceUtil.extractFirstWord(result, "item");
        }
        taxiData.put("itemName", itemName);

        // 提取金额
        String amount = "";
        if (isVatInvoice && result.containsKey("AmountInFiguers")) {
            amount = InvoiceUtil.extractFirstWord(result, "AmountInFiguers");
        } else if (result.containsKey("TotalFare")) {
            amount = InvoiceUtil.extractFirstWord(result, "TotalFare");
        } else {
            amount = InvoiceUtil.extractAmount(result);
        }
        taxiData.put("amount", amount);

        // 累加打车票金额
        totalTaxiAmount = InvoiceUtil.addNumericAmount(totalTaxiAmount, amount);

        // 提取发票号/票号和日期
        if (isVatInvoice) {
            InvoiceUtil.extractField(result, "InvoiceNum", taxiData, "invoiceNum");
            InvoiceUtil.extractField(result, "InvoiceDate", taxiData, "invoiceDate");
            InvoiceUtil.extractField(result, "SellerName", taxiData, "sellerName");
        } else {
            if (!InvoiceUtil.extractField(result, "InvoiceNum", taxiData, "invoiceNum")) {
                InvoiceUtil.extractField(result, "invoice_num", taxiData, "invoiceNum");
            }
            if (!InvoiceUtil.extractField(result, "Date", taxiData, "invoiceDate")) {
                InvoiceUtil.extractField(result, "date", taxiData, "invoiceDate");
            }
            if (!InvoiceUtil.extractField(result, "Location", taxiData, "sellerName")) {
                InvoiceUtil.extractField(result, "seller", taxiData, "sellerName");
            }
        }

        // 特殊字段 - 出租车号牌
        if (result.containsKey("TaxiNum")) {
            InvoiceUtil.extractField(result, "TaxiNum", taxiData, "taxiNumber");
        }

        // 特殊字段 - 行程信息
        String journeyTime = InvoiceUtil.extractJourneyTime(result);
        if (!journeyTime.isEmpty()) {
            taxiData.put("journeyTime", journeyTime);
        }

        // 添加到打车票结果集
        taxiTickets.put(taxiData);

        return totalTaxiAmount;
    }

    /**
     * 处理餐费票据 - 提取相关信息并添加到结果集
     */
    private double processMealTicket(com.alibaba.fastjson.JSONObject result, String type,
                                     JSONArray mealTickets, double totalMealAmount) throws JSONException {
        JSONObject mealData = new JSONObject();
        mealData.put("ticketType", type);

        // 根据票据类型选择正确的字段名
        boolean isVatInvoice = VAT_TYPES.contains(type);

        // 提取购买方信息
        if (isVatInvoice) {
            InvoiceUtil.extractField(result, "PurchaserName", mealData, "purchaserName");
            InvoiceUtil.extractField(result, "PurchaserRegisterNum", mealData, "purchaserTaxNum");
        } else {
            InvoiceUtil.extractField(result, "buyer", mealData, "purchaserName");
            InvoiceUtil.extractField(result, "buyer_tax_id", mealData, "purchaserTaxNum");
        }

        // 提取项目名称
        String itemName = "";
        if (isVatInvoice && result.containsKey("CommodityName")) {
            itemName = InvoiceUtil.extractCommodityNames(result, "CommodityName");
        } else if (result.containsKey("item")) {
            // 原有的item处理逻辑
            itemName = InvoiceUtil.extractItemNames(result, "item");
        }
        mealData.put("itemName", itemName);

        // 提取金额 - 增值税发票使用AmountInFiguers
        String amount = "";
        if (isVatInvoice && result.containsKey("AmountInFiguers")) {
            amount = InvoiceUtil.extractFirstWord(result, "AmountInFiguers");
        } else {
            amount = InvoiceUtil.extractAmount(result);
        }
        mealData.put("amount", amount);

        // 累加餐费票据金额
        totalMealAmount = InvoiceUtil.addNumericAmount(totalMealAmount, amount);

        // 提取发票号和日期
        if (isVatInvoice) {
            InvoiceUtil.extractField(result, "InvoiceNum", mealData, "invoiceNum");
            InvoiceUtil.extractField(result, "InvoiceDate", mealData, "invoiceDate");
            InvoiceUtil.extractField(result, "SellerName", mealData, "sellerName");
        } else {
            InvoiceUtil.extractField(result, "invoice_num", mealData, "invoiceNum");
            InvoiceUtil.extractField(result, "date", mealData, "invoiceDate");
            InvoiceUtil.extractField(result, "seller", mealData, "sellerName");
        }

        // 添加到餐费票据结果集
        mealTickets.put(mealData);

        return totalMealAmount;
    }

    /**
     * 处理增值税发票 - 提取相关信息并添加到结果集
     */
    private double processVatTicket(com.alibaba.fastjson.JSONObject result, String type,
                                    JSONArray vatReceipts, double totalVatAmount) throws JSONException {
        JSONObject vatData = new JSONObject();
        vatData.put("ticketType", type);

        // 提取购买方信息
        InvoiceUtil.extractField(result, "PurchaserName", vatData, "purchaserName");
        InvoiceUtil.extractField(result, "PurchaserRegisterNum", vatData, "purchaserRegisterNum");

        // 提取商品名称
        String commodityNames = InvoiceUtil.extractCommodityNames(result, "CommodityName");
        vatData.put("commodityName", commodityNames);

        // 提取金额
        String amount = "";
        if (result.containsKey("AmountInFiguers")) {
            amount = InvoiceUtil.extractFirstWord(result, "AmountInFiguers");
        } else {
            amount = InvoiceUtil.extractAmount(result);
        }
        vatData.put("amount", amount);

        // 累加增值税发票金额
        totalVatAmount = InvoiceUtil.addNumericAmount(totalVatAmount, amount);

        // 提取发票号和日期
        InvoiceUtil.extractField(result, "InvoiceNum", vatData, "invoiceNum");
        InvoiceUtil.extractField(result, "InvoiceDate", vatData, "invoiceDate");
        InvoiceUtil.extractField(result, "SellerName", vatData, "sellerName");

        // 添加到增值税发票结果集
        vatReceipts.put(vatData);

        return totalVatAmount;
    }

    /**
     * 获取差旅标准
     */
    private Map<String, Integer> getTravelStandard(String city, String positionCode) {
        if (InvoiceUtil.isEmpty(city) || InvoiceUtil.isEmpty(positionCode)) {
            return null;
        }

        String positionPrefix = InvoiceUtil.getPositionPrefix(positionCode);
        if (positionPrefix == null) {
            return null;
        }

        RecordSet rs = new RecordSet();
        Map<String, Integer> standards = null;

        try {
            // 匹配城市
            String sql = "SELECT " +
                    positionPrefix + "zsbx AS accom_standard, " +
                    positionPrefix + "shbz AS living_allowance, " +
                    positionPrefix + "jtbz AS transport_allowance " +
                    "FROM uf_travel_standards WHERE cs = ?";

            rs.executeQuery(sql, city);

            if (rs.next()) {
                standards = new HashMap<>();
                standards.put("accommodationStandard", rs.getInt("accom_standard"));
                standards.put("livingAllowance", rs.getInt("living_allowance"));
                standards.put("transportAllowance", rs.getInt("transport_allowance"));
                return standards;
            }

            // 匹配省份
            sql = "SELECT sf FROM uf_travel_standards WHERE cs = ?";
            rs.executeQuery(sql, city);

            String province = null;
            if (rs.next()) {
                province = rs.getString("sf");
            }

            if (!InvoiceUtil.isEmpty(province)) {
                sql = "SELECT " +
                        positionPrefix + "zsbx AS accom_standard, " +
                        positionPrefix + "shbz AS living_allowance, " +
                        positionPrefix + "jtbz AS transport_allowance " +
                        "FROM uf_travel_standards WHERE sf = ? AND (cs IS NULL OR cs = '')";

                rs.executeQuery(sql, province);

                if (rs.next()) {
                    standards = new HashMap<>();
                    standards.put("accommodationStandard", rs.getInt("accom_standard"));
                    standards.put("livingAllowance", rs.getInt("living_allowance"));
                    standards.put("transportAllowance", rs.getInt("transport_allowance"));
                    return standards;
                }
            }

            // 最后尝试获取默认标准
            sql = "SELECT " +
                    positionPrefix + "zsbx AS accom_standard, " +
                    positionPrefix + "shbz AS living_allowance, " +
                    positionPrefix + "jtbz AS transport_allowance " +
                    "FROM uf_travel_standards WHERE (sf IS NULL OR sf = '') AND (cs IS NULL OR cs = '') " +
                    "AND (qy IS NOT NULL AND qy != '') LIMIT 1";

            rs.execute(sql);

            if (rs.next()) {
                standards = new HashMap<>();
                standards.put("accommodationStandard", rs.getInt("accom_standard"));
                standards.put("livingAllowance", rs.getInt("living_allowance"));
                standards.put("transportAllowance", rs.getInt("transport_allowance"));
                return standards;
            }

        } catch (Exception e) {
        }

        return null;
    }
}