package com.api.workflow.yytex.Invoice.service;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * 报销计算器 - 统一处理超标计算逻辑
 * 消除餐费和打车费计算中的重复代码
 */
public class ReimbursementCalculator {

    /**
     * 超标计算结果
     */
    public static class OverBudgetResult {
        public final double reimbursableAmount;
        public final double personalExpense;
        public final JSONObject calculationData;

        public OverBudgetResult(double reimbursableAmount, double personalExpense, JSONObject calculationData) {
            this.reimbursableAmount = reimbursableAmount;
            this.personalExpense = personalExpense;
            this.calculationData = calculationData;
        }
    }

    /**
     * 计算超标费用的报销金额
     * @param actualAmount 实际费用金额
     * @param standardAmount 标准金额
     * @param expenseType 费用类型（如"餐费"、"打车费"）
     * @param standardType 标准类型（如"生活补贴标准"、"交通补贴标准"）
     * @return 超标计算结果
     */
    public static OverBudgetResult calculateOverBudgetReimbursement(
            double actualAmount, double standardAmount, 
            String expenseType, String standardType) throws JSONException {
        
        double reimbursableAmount;
        double personalExpense = 0;
        JSONObject calculationData = new JSONObject();

        if (actualAmount <= standardAmount) {
            // 费用未超标，全额报销
            reimbursableAmount = actualAmount;
            calculationData.put("计算方式", expenseType + "未超标，全额报销")
                    .put(expenseType + "金额", String.format("%.2f元", actualAmount))
                    .put(standardType, String.format("%.2f元", standardAmount))
                    .put("可报销" + expenseType + "金额", String.format("%.2f元", reimbursableAmount));
        } else {
            // 费用超标，计算超出部分
            double overBudget = actualAmount - standardAmount;
            double overBudgetRatio = overBudget / standardAmount;

            if (overBudgetRatio <= 0.2) {
                // 超支20%以内，个人承担超额部分的50%
                personalExpense = overBudget * 0.5;
                reimbursableAmount = actualAmount - personalExpense;
                calculationData.put("计算方式", expenseType + "超标20%以内，个人承担超额部分的50%")
                        .put(expenseType + "金额", String.format("%.2f元", actualAmount))
                        .put(standardType, String.format("%.2f元", standardAmount))
                        .put("超出金额", String.format("%.2f元", overBudget))
                        .put("超出比例", String.format("%.2f%%", overBudgetRatio * 100))
                        .put("个人承担金额", String.format("%.2f元", personalExpense))
                        .put("可报销" + expenseType + "金额", String.format("%.2f元", reimbursableAmount));
            } else {
                // 超支20%以上，仅报销标准额度
                personalExpense = overBudget;
                reimbursableAmount = standardAmount;
                calculationData.put("计算方式", expenseType + "超标20%以上，仅报销标准额度")
                        .put(expenseType + "金额", String.format("%.2f元", actualAmount))
                        .put(standardType, String.format("%.2f元", standardAmount))
                        .put("超出金额", String.format("%.2f元", overBudget))
                        .put("超出比例", String.format("%.2f%%", overBudgetRatio * 100))
                        .put("个人承担金额", String.format("%.2f元", personalExpense))
                        .put("可报销" + expenseType + "金额", String.format("%.2f元", reimbursableAmount));
            }
        }

        return new OverBudgetResult(reimbursableAmount, personalExpense, calculationData);
    }

    /**
     * 计算住宿费用超标处理
     * @param actualAccomAmount 实际住宿费用
     * @param accomStandardTotal 住宿标准总额
     * @param calculationProcess 计算过程数组
     * @return 可报销的住宿费用
     */
    public static double calculateAccommodationOverBudget(
            double actualAccomAmount, double accomStandardTotal, 
            JSONArray calculationProcess) throws JSONException {
        
        if (actualAccomAmount <= accomStandardTotal) {
            // 有发票且未超支：以实际住宿费用为准
            calculationProcess.put(new JSONObject()
                    .put("step", "住宿费用未超标")
                    .put("description", "住宿费用未超过标准")
                    .put("data", new JSONObject()
                            .put("可报销住宿费用", String.format("%.2f元", actualAccomAmount))
                    )
            );
            return actualAccomAmount;
        } else {
            // 超出金额 = 实际住宿费用 - 住宿标准总额
            double overBudget = actualAccomAmount - accomStandardTotal;
            // 超支比例 = 超出金额 / 住宿标准总额
            double overBudgetRatio = overBudget / accomStandardTotal;

            calculationProcess.put(new JSONObject()
                    .put("step", "住宿费用超标分析")
                    .put("description", "住宿费用超过标准的计算")
                    .put("data", new JSONObject()
                            .put("超出金额", String.format("%.2f元", overBudget))
                            .put("超出比例", String.format("%.2f%%", overBudgetRatio * 100))
                    )
            );

            if (overBudgetRatio <= 0.2) {
                // 超支20%以内：个人承担超额部分的50%
                double personalExpense = overBudget * 0.5;  // 个人承担费用
                // 可报销的住宿费用 = 实际住宿费用 - 个人承担部分
                double reimburseAccomAmount = actualAccomAmount - personalExpense;

                calculationProcess.put(new JSONObject()
                        .put("step", "住宿费用超标处理(≤20%)")
                        .put("description", "超标20%以内，个人承担超额部分的50%")
                        .put("data", new JSONObject()
                                .put("个人承担费用", String.format("%.2f元", personalExpense))
                                .put("计算公式", "实际住宿费用 - 个人承担部分")
                                .put("计算明细", String.format("%.2f - %.2f = %.2f元",
                                        actualAccomAmount, personalExpense, reimburseAccomAmount))
                        )
                );
                return reimburseAccomAmount;
            } else {
                // 超支20%以上：住宿仅报销标准额度
                calculationProcess.put(new JSONObject()
                        .put("step", "住宿费用超标处理(>20%)")
                        .put("description", "超标20%以上，仅报销标准额度")
                        .put("data", new JSONObject()
                                .put("可报销住宿费用", String.format("%.2f元", accomStandardTotal))
                                .put("超额部分个人承担", String.format("%.2f元", actualAccomAmount - accomStandardTotal))
                        )
                );
                return accomStandardTotal;
            }
        }
    }

    /**
     * 添加基本信息到计算过程
     */
    public static void addBasicInfoToCalculationProcess(
            JSONArray calculationProcess, int days, int nights, int numberOfTravelers,
            boolean useHuazhuhui, int accomStandard, int livingStandard, int transportStandard,
            double accomStandardTotal, double actualAllowanceTotal) throws JSONException {
        
        JSONObject basicInfoData = new JSONObject()
                .put("出差天数", days + "天")
                .put("住宿晚数", nights + "晚")
                .put("出差人数", numberOfTravelers + "人")
                .put("华住会酒店", useHuazhuhui ? "是" : "否")
                .put("住宿标准", accomStandard + "元/晚/人")
                .put("生活补贴标准", livingStandard + "元/天/人")
                .put("交通补贴标准", transportStandard + "元/天/人");

        if (useHuazhuhui) {
            basicInfoData.put("华住会政策", "住宿费用全额报销");
        }

        basicInfoData.put("住宿标准总额", String.format("%.2f元", accomStandardTotal));
        basicInfoData.put("补贴总额", String.format("%.2f元", actualAllowanceTotal));

        calculationProcess.put(new JSONObject()
                .put("step", "基本信息")
                .put("description", "报销计算基础数据")
                .put("data", basicInfoData)
        );
    }

    /**
     * 添加实际费用信息到计算过程
     */
    public static void addActualExpenseToCalculationProcess(
            JSONArray calculationProcess, double totalTransportAmount, double totalVatAmount,
            double totalMealAmount, double totalTaxiAmount, double totalActual) throws JSONException {
        
        calculationProcess.put(new JSONObject()
                .put("step", "实际费用")
                .put("description", "各类票据实际金额")
                .put("data", new JSONObject()
                        .put("交通票金额", String.format("%.2f元", totalTransportAmount))
                        .put("住宿发票金额", String.format("%.2f元", totalVatAmount))
                        .put("餐费票据金额", String.format("%.2f元", totalMealAmount))
                        .put("打车票据金额", String.format("%.2f元", totalTaxiAmount))
                        .put("实际费用总计", String.format("%.2f元", totalActual))
                )
        );
    }

    /**
     * 添加补贴报销汇总到计算过程
     */
    public static void addAllowanceSummaryToCalculationProcess(
            JSONArray calculationProcess, double reimbursableMealAmount, 
            double reimbursableTaxiAmount, double mealPersonalExpense, 
            double taxiPersonalExpense, double actualAllowanceTotal) throws JSONException {
        
        calculationProcess.put(new JSONObject()
                .put("step", "补贴报销汇总")
                .put("description", "餐费与打车费可报销金额汇总")
                .put("data", new JSONObject()
                        .put("可报销餐费金额", String.format("%.2f元", reimbursableMealAmount))
                        .put("可报销打车费金额", String.format("%.2f元", reimbursableTaxiAmount))
                        .put("个人总承担金额", String.format("%.2f元", mealPersonalExpense + taxiPersonalExpense))
                        .put("实际可报销补贴总额", String.format("%.2f元", actualAllowanceTotal))
                )
        );
    }

    /**
     * 添加最终报销金额计算到计算过程
     */
    public static void addFinalCalculationToCalculationProcess(
            JSONArray calculationProcess, double totalTransportAmount, 
            double reimburseAccomAmount, double reimbursableMealAmount, 
            double reimbursableTaxiAmount, double totalExpenseAmount) throws JSONException {
        
        calculationProcess.put(new JSONObject()
                .put("step", "最终报销金额计算")
                .put("description", "汇总各项费用得出最终报销金额")
                .put("data", new JSONObject()
                        .put("计算公式", "交通票金额 + 可报销住宿费用 + 可报销餐费 + 可报销打车费")
                        .put("计算明细", String.format("%.2f + %.2f + %.2f + %.2f = %.2f元",
                                totalTransportAmount, reimburseAccomAmount, reimbursableMealAmount, 
                                reimbursableTaxiAmount, totalExpenseAmount))
                )
        );
    }

    /**
     * 添加人均费用计算到计算过程（多人出差时）
     */
    public static void addPerPersonCalculationToCalculationProcess(
            JSONArray calculationProcess, double totalExpenseAmount, int numberOfTravelers) throws JSONException {
        
        if (numberOfTravelers > 1) {
            double perPersonExpense = totalExpenseAmount / numberOfTravelers;
            calculationProcess.put(new JSONObject()
                    .put("step", "人均费用计算")
                    .put("description", "计算每人平均报销金额")
                    .put("data", new JSONObject()
                            .put("总报销金额", String.format("%.2f元", totalExpenseAmount))
                            .put("出差人数", numberOfTravelers + "人")
                            .put("人均报销金额", String.format("%.2f元/人", perPersonExpense))
                    )
            );
        }
    }

    /**
     * 添加最终报销结果到计算过程
     */
    public static void addFinalResultToCalculationProcess(
            JSONArray calculationProcess, double totalExpenseAmount, 
            int numberOfTravelers, boolean useHuazhuhui) throws JSONException {
        
        calculationProcess.put(new JSONObject()
                .put("step", "报销结果")
                .put("description", "最终可报销金额")
                .put("data", new JSONObject()
                        .put("可报销总金额", String.format("%.2f元", totalExpenseAmount))
                        .put("出差人数", numberOfTravelers + "人")
                        .put("华住会酒店", useHuazhuhui ? "住宿费用全额报销" : "未使用")
                )
        );
    }
}
