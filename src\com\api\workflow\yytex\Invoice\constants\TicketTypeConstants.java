package com.api.workflow.yytex.Invoice.constants;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 票据类型常量
 */
public final class TicketTypeConstants {
    // 交通票据类型
    public static final Set<String> TRANSPORT_TYPES = new HashSet<>(Arrays.asList(
            "train_ticket", "air_ticket", "bus_ticket", "ferry_ticket"
    ));

    // 打车票据类型
    public static final Set<String> TAXI_TYPES = new HashSet<>(Arrays.asList(
            "taxi_receipt", "taxi_online_ticket", "toll_invoice"
    ));

    // 餐费票据类型
    public static final Set<String> MEAL_TYPES = new HashSet<>(Arrays.asList(
            "quota_invoice", "roll_normal_invoice", "printed_invoice",
            "printed_elec_invoice", "limit_invoice", "shopping_receipt", "pos_invoice"
    ));

    // 增值税发票类型
    public static final Set<String> VAT_TYPES = new HashSet<>(Arrays.asList(
            "vat_invoice", "motor_vehicle_invoice", "used_vehicle_invoice"
    ));

    // 禁止实例化
    private TicketTypeConstants() {}
}