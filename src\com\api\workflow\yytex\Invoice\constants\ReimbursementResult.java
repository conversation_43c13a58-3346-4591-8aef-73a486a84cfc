package com.api.workflow.yytex.Invoice.constants;

import org.json.JSONArray;
import org.json.JSONObject;

/**
 * 报销金额计算结果类 - 包含标准信息和计算过程
 */
public class ReimbursementResult {
    // 总计可报销金额
    public final double totalExpenseAmount;
    // 差旅标准信息
    public final JSONObject standardInfo;
    // 计算过程详情
    public final JSONArray calculationProcess;

    /**
     * 构造函数
     * @param totalExpenseAmount 总计可报销金额
     * @param standardInfo 差旅标准信息
     * @param calculationProcess 计算过程详情
     */
    public ReimbursementResult(double totalExpenseAmount, JSONObject standardInfo, JSONArray calculationProcess) {
        this.totalExpenseAmount = totalExpenseAmount;
        this.standardInfo = standardInfo;
        this.calculationProcess = calculationProcess;
    }

    /**
     * 获取总计可报销金额
     */
    public double getTotalExpenseAmount() {
        return totalExpenseAmount;
    }

    /**
     * 获取差旅标准信息
     */
    public JSONObject getStandardInfo() {
        return standardInfo;
    }

    /**
     * 获取计算过程详情
     */
    public JSONArray getCalculationProcess() {
        return calculationProcess;
    }
}