<script>  
jQuery(document).ready(function() {
    // 获取requestid
    var requestid = WfForm.getBaseInfo().requestid;
    
    // 常量定义  
    const DETAIL_TABLES = {  
        TRANSPORT: "detail_3",
        PRIVATE_CAR: "detail_4",      // 【新增】私车公用明细表
        VAT: "detail_5",  
        MEAL: "detail_6",  
        TAXI: "detail_7"  
    };  
    
    // 字段名称映射定义 - 根据实际表格列名更新  
    const FIELD_NAMES = {  
        transport: {  
            "name": "xm",                 // 姓名  
            "dateTime": "ccrq",           // 乘车日期  
            "startStation": "sfd",        // 始发地  
            "destStation": "mdd",         // 目的地  
            "transportNum": "cc",         // 交通工具编号/车次  
            "amount": "je",               // 金额  
            "invoiceNum": "pjhm",         // 票据号码  
            "seatType": "zwlx"            // 座位类型  
        },
        // 【新增】私车公用字段映射
        privateCar: {
            "purchaserName": "gmfmc",     // 购买方名称
            "purchaserTaxNum": "gmfsh",   // 购买方税号
            "itemName": "xmmc",           // 项目名称
            "amount": "je",               // 金额
            "invoiceNum": "fphm",         // 发票号码
            "invoiceDate": "kprq",        // 开票日期
            "sellerName": "xsfmc",        // 销售方名称
            "specification": "ggxh",      // 规格型号
            "productType": "cpy"          // 成品油/服务类型
        },
        vat: {  
            "purchaserName": "gmfmc",         // 购买方名称  
            "purchaserRegisterNum": "gmfhm",  // 购买方号码  
            "commodityName": "xmmc",          // 项目名称  
            "amount": "je",                   // 金额  
            "invoiceNum": "fphm",             // 发票号码  
            "invoiceDate": "kprq",            // 开票日期  
            "sellerName": "xsfmc"             // 销售方名称  
        },  
        meal: {  
            "purchaserName": "gmfmc",         // 购买方名称   
            "purchaserTaxNum": "gmfsh",       // 购买方税号  
            "itemName": "xmmc",               // 项目名称  
            "amount": "je",                   // 金额  
            "invoiceNum": "fphm",             // 发票号码  
            "invoiceDate": "kprq",            // 开票日期  
            "sellerName": "xsfmc"             // 销售方名称  
        },  
        taxi: {  
            "purchaserName": "gmfmc",         // 购买方名称  
            "purchaserTaxNum": "gmfsh",       // 购买方税号  
            "itemName": "xmmc",               // 项目名称  
            "amount": "je",                   // 金额  
            "invoiceNum": "fphm",             // 发票号码  
            "invoiceDate": "kprq",            // 开票日期  
            "sellerName": "xsfmc",            // 销售方名称  
            "taxService": "lkysfw"            // 旅客运输服务  
        }  
    };  
    
    // 初始化UI  
    function initUI() {  
        $("#btnLink").html('<span><button id="btnWf" title="处理票据" type="button" class="ant-btn ant-btn-primary"><div class="wf-req-top-button">处理票据</div></button></span>');  
        $("#btnWf").click(processReceipts);  
    }  
    
    // 验证表单数据  
    function validateForm(attachments, positionCode, days) {  
        // 【修改】添加私车公用票据验证
        if (!attachments.jtpj && !attachments.zsfp && !attachments.cffp && 
            !attachments.dcfp && !attachments.scgy) {
            alert("请至少上传一种票据");  
            return false;  
        }  
        
        if (!positionCode) {  
            alert("请选择职位代码");  
            return false;  
        }  
        
        if (!days) {  
            alert("请填写报销天数");  
            return false;  
        }  
        
        return true;  
    }  
    
    // 处理票据主函数  
    function processReceipts() {
        // 获取表单数据  
        const attachments = {  
            jtpj: $('#field18471').val(),  // 交通票附件
            zsfp: $('#field18502').val(),  // 增值税发票附件  
            cffp: $('#field18537').val(),  // 餐费发票附件  
            dcfp: $('#field18546').val(),  // 打车费票据附件
            scgy: $('#field18551').val()   // 【新增】私车公用票据附件
        };  
        
        const formData = {  
            zj: WfForm.getFieldValue("field18499"),  // 职位代码  
            ts: WfForm.getFieldValue("field18500"),  // 报销天数  
            cs: WfForm.getFieldValue("field18522")   // 城市  
        };
        
        // 获取出差人数
        var ccrs = WfForm.getFieldValue("field18549");
        // 判断是否华住会
        var sfhzh = WfForm.getFieldValue("field18529");
        
        // 验证表单  
        if (!validateForm(attachments, formData.zj, formData.ts)) {  
            return;  
        }  
        
        // 准备请求数据  
        const requestData = {  
            transportTickets: attachments.jtpj || "",    // 交通发票附件ID  
            vatAttachIds: attachments.zsfp || "",        // 增值税发票附件ID  
            mealAttachIds: attachments.cffp || "",       // 餐费发票附件ID  
            taxiAttachIds: attachments.dcfp || "",       // 打车费票据附件ID  
            privateCarAttachIds: attachments.scgy || "", // 【新增】私车公用票据附件ID
            
            positionCode: formData.zj,                   // 职位代码  
            days: formData.ts,                           // 天数  
            city: formData.cs,                           // 城市  
            requestid: requestid,                        // 添加requestid
            numberOfTravelers: ccrs,                     // 出差人数 
            isHuazhuhui: sfhzh                           // 是否华住会
        };  
        
        // 设置按钮状态  
        const $btn = $("#btnWf");  
        const originalText = $btn.find("div").text();  
        $btn.attr("disabled", true);  
        $btn.find("div").text("处理中...");  
        
        // 发送请求  
        sendRequest(requestData, $btn, originalText);  
    }  
    
    // 发送API请求  
    function sendRequest(requestData, $btn, originalText) {  
        jQuery.ajax({  
            url: '/api/workflow/yytex/processAttachments',  
            type: 'POST',  
            contentType: 'application/json',  
            data: JSON.stringify(requestData),  
            success: function(response) {  
                // 恢复按钮状态  
                restoreButtonState($btn, originalText);  
                
                // 处理响应  
                if (response.success) {  
                    handleSuccessResponse(response);  
                } else {  
                    alert("处理失败: " + (response.message || "未知错误"));  
                }  
            },  
            error: function(xhr, status, error) {  
                // 恢复按钮状态  
                restoreButtonState($btn, originalText);  
                handleErrorResponse(xhr, error);  
            }  
        });  
    }  
    
    // 恢复按钮状态  
    function restoreButtonState($btn, originalText) {  
        $btn.attr("disabled", false);  
        $btn.find("div").text(originalText);  
    }  
    
    // 处理成功响应  
    function handleSuccessResponse(response) {  
        // 更新总金额  
        updateTotalAmount(response.totalExpenseAmount);  
        
        // 清空并更新明细表  
        const detailOptions = [  
            { tableId: DETAIL_TABLES.TRANSPORT, data: response.transportTickets, fieldNames: FIELD_NAMES.transport },
            { tableId: DETAIL_TABLES.PRIVATE_CAR, data: response.privateCarTickets, fieldNames: FIELD_NAMES.privateCar }, // 【新增】
            { tableId: DETAIL_TABLES.VAT, data: response.vatReceipts, fieldNames: FIELD_NAMES.vat },  
            { tableId: DETAIL_TABLES.MEAL, data: response.mealTickets, fieldNames: FIELD_NAMES.meal },  
            { tableId: DETAIL_TABLES.TAXI, data: response.taxiTickets, fieldNames: FIELD_NAMES.taxi }  
        ];  
        
        // 处理所有明细表  
        detailOptions.forEach(option => {  
            processDetailTable(option.tableId, option.data, option.fieldNames);  
        });  
        
        // 显示各类票据总金额  
        try {  
            if (response.totalTransportAmount > 0) {
                WfForm.changeFieldValue("field_transport_total", {value: response.totalTransportAmount.toFixed(2)});  
            }
            // 【新增】私车公用总额
            if (response.totalPrivateCarAmount > 0) {
                WfForm.changeFieldValue("field_private_car_total", {value: response.totalPrivateCarAmount.toFixed(2)});  
            }
            if (response.totalVatAmount > 0) {
                WfForm.changeFieldValue("field_vat_total", {value: response.totalVatAmount.toFixed(2)});  
            }  
            if (response.totalMealAmount > 0) {
                WfForm.changeFieldValue("field_meal_total", {value: response.totalMealAmount.toFixed(2)});  
            }  
            if (response.totalTaxiAmount > 0) {
                WfForm.changeFieldValue("field_taxi_total", {value: response.totalTaxiAmount.toFixed(2)});  
            }  
        } catch(e) {  
            console.error("更新票据总额失败", e);  
        }  
        
        // 显示差旅标准信息（如果有）  
        if (response.standardInfo) {  
            try {  
                const info = response.standardInfo;  
                // 这里可以根据实际表单字段ID显示差旅标准信息  
                // 例如: WfForm.changeFieldValue("field_accom_standard", {value: info.accommodationStandard});  
            } catch(e) {  
                console.error("更新差旅标准信息失败", e);  
            }  
        }
        
        // 处理报销计算过程并显示在多行文本框中
        if (response.calculationProcess && response.calculationProcess.length > 0) {
            try {
                const processText = formatCalculationProcess(response.calculationProcess);
                // 将格式化后的文本放入字段ID为field18550的文本框
                WfForm.changeFieldValue("field18550", {value: processText});
            } catch(e) {
                console.error("更新报销计算过程失败", e);
            }
        }
    }  
    
    /**
     * 格式化报销计算过程为易读的文本
     * @param {Array} calculationProcess 计算过程数据数组
     * @returns {string} 格式化后的文本
     */
    function formatCalculationProcess(calculationProcess) {
        if (!calculationProcess || !calculationProcess.length) {
            return "未提供计算过程";
        }
        
        let resultText = "【报销金额计算过程】\n";
        resultText += "========================================\n\n";
        
        // 遍历每个计算步骤
        calculationProcess.forEach((step, index) => {
            // 添加步骤标题和描述
            resultText += `【${index + 1}】${step.step}：${step.description}\n`;
            resultText += "----------------------------------------\n";
            
            // 添加步骤数据
            if (step.data) {
                for (const key in step.data) {
                    resultText += `${key}：${step.data[key]}\n`;
                }
            }
            
            // 在每个步骤后添加空行（最后一步除外）
            if (index < calculationProcess.length - 1) {
                resultText += "\n";
            }
        });
        
        // 添加结束分隔线
        resultText += "\n========================================";
        
        return resultText;
    }
    
    // 处理错误响应  
    function handleErrorResponse(xhr, error) {  
        let errorMsg = "票据处理请求失败";  
        
        try {  
            const errorObj = JSON.parse(xhr.responseText);  
            if (errorObj && errorObj.message) {  
                errorMsg += ": " + errorObj.message;  
            } else {  
                errorMsg += ": " + error;  
            }  
        } catch(e) {  
            errorMsg += ": " + error;  
        }  
        
        alert(errorMsg);  
    }  
    
    // 更新总金额  
    function updateTotalAmount(amount) {  
        const expenseAmount = amount || 0;  
        
        if (expenseAmount > 0) {  
            try {  
                WfForm.changeFieldValue("field18501", {value: expenseAmount.toFixed(2)});  
            } catch(e) {  
                console.error("更新总金额失败", e);  
            }  
        }  
    }  
    
    /**  
     * 统一处理明细表数据添加  
     * @param {string} tableId 明细表ID  
     * @param {Array} items 数据项数组  
     * @param {Object} fieldNames 字段名映射  
     */  
    function processDetailTable(tableId, items, fieldNames) {
        // 如果没有数据，直接返回  
        if (!items || !items.length) {  
            return;  
        }  
        
        // 先清空明细表  
        WfForm.delDetailRow(tableId, "all");  
        
        // 生成字段ID映射  
        const fieldMapping = {};  
        for (const key in fieldNames) {  
            fieldMapping[key] = getFieldId(fieldNames[key], tableId);  
        }  
        
        // 添加数据到明细表  
        for (let i = 0; i < items.length; i++) {  
            const item = items[i];  
            const rowData = {};  
            
            // 设置每个字段的值  
            for (const field in fieldMapping) {  
                const fieldId = fieldMapping[field];  
                if (fieldId && item[field] !== undefined) {  
                    rowData[fieldId] = {value: item[field] };  
                }  
            }  
            
            // 添加明细行  
            try {  
                WfForm.addDetailRow(tableId, rowData);  
            } catch (e) {  
                console.error(`添加明细行失败: ${tableId}`, e);  
            }  
        }  
    }  
    
    /**  
     * 获取字段ID - 用于动态获取字段ID  
     * @param {string} fieldName 字段名称  
     * @param {string} detailTableId 明细表ID  
     * @returns {string} 字段ID  
     */  
    function getFieldId(fieldName, detailTableId) {
        try {  
            return WfForm.convertFieldNameToId(fieldName, detailTableId);  
        } catch (e) {  
            console.error("获取字段ID失败", fieldName, detailTableId, e);  
            return null;  
        }  
    }  
    
    // 初始化  
    initUI();  
});    
</script>