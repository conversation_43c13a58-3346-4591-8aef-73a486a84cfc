package com.api.workflow.yytex.Invoice.controller;

import javax.ws.rs.*;
import javax.ws.rs.core.*;
import org.json.JSONObject;
import org.json.JSONException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.api.workflow.yytex.Invoice.constants.ErrorMessages;
import com.api.workflow.yytex.Invoice.constants.ServerConfig;
import com.api.workflow.yytex.Invoice.service.InvoiceRecognitionService;

/**
 * 交通票、发票、餐费票据和打车票据自动识别及报销处理 RESTful 服务
 */
@Path("/workflow/yytex")
public class InvoiceRecognition {
    // 创建服务类实例
    private InvoiceRecognitionService service = new InvoiceRecognitionService(
            ServerConfig.IP, ServerConfig.TICKET_API_URL);

    /**
     * 票据识别处理 RESTful 端点 - 基于附件ID处理
     */
    @POST
    @Path("/processAttachments")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response processAttachments(String jsonInput) throws JSONException {
        try {
            // 解析输入JSON
            JSONObject inputData = new JSONObject(jsonInput);

            // 获取必要参数
            String requestid = inputData.optString("requestid", "");
            String transportAttachIds = inputData.optString("transportTickets", "");
            String vatAttachIds = inputData.optString("vatAttachIds");
            String mealAttachIds = inputData.optString("mealAttachIds");
            String taxiAttachIds = inputData.optString("taxiAttachIds");

            // 验证必填参数
            if (!inputData.has("positionCode")) {
                return buildErrorResponse(400, ErrorMessages.MISSING_POSITION_CODE);
            }

            if (!inputData.has("days")) {
                return buildErrorResponse(400, ErrorMessages.MISSING_DAYS);
            }

            if (!inputData.has("city")) {
                return buildErrorResponse(400, ErrorMessages.MISSING_CITY);
            }

            String positionCode = inputData.getString("positionCode");
            int days = Integer.parseInt(inputData.getString("days"));
            String city = inputData.getString("city");

            // 获取新增的参数 - 出差人数和是否华住会
            int numberOfTravelers = 1; // 默认为1人
            String isHuazhuhui = "1";  // 默认为不是华住会

            // 尝试从输入数据中获取出差人数
            if (inputData.has("numberOfTravelers")) {
                try {
                    numberOfTravelers = Integer.parseInt(inputData.getString("numberOfTravelers"));
                    // 确保出差人数至少为1
                    if (numberOfTravelers < 1) {
                        numberOfTravelers = 1;
                    }
                } catch (Exception e) {
                    // 解析失败时使用默认值
                }
            }

            // 尝试从输入数据中获取是否华住会
            if (inputData.has("isHuazhuhui")) {
                isHuazhuhui = inputData.getString("isHuazhuhui");
            }

            // 调用服务类处理业务逻辑
            JSONObject result = service.processAttachments(
                    transportAttachIds, vatAttachIds, mealAttachIds, taxiAttachIds,
                    positionCode, days, city, requestid,numberOfTravelers,isHuazhuhui);

            return Response.ok(result.toString(), MediaType.APPLICATION_JSON).build();

        } catch (NumberFormatException e) {
            return buildErrorResponse(400, "无效的数字格式: " + e.getMessage());
        } catch (Exception e) {
            return buildErrorResponse(500, "处理失败: " + e.getMessage());
        }
    }

    /**
     * 构建错误响应
     */
    private Response buildErrorResponse(int status, String message) throws JSONException {
        JSONObject errorResponse = new JSONObject();
        errorResponse.put("success", false);
        errorResponse.put("message", message);
        return Response.status(status).entity(errorResponse.toString()).build();
    }

    /**
     * 简单测试端点
     */
    @GET
    @Path("/test")
    @Produces(MediaType.TEXT_PLAIN)
    public String test(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        return "服务运行正常 - " + new java.util.Date();
    }
}