package com.api.workflow.yytex.Invoice.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.json.JSONException;
import weaver.conn.RecordSet;

import java.util.ArrayList;
import java.util.List;

import com.api.workflow.yytex.Invoice.constants.FieldConstants;
import com.api.workflow.yytex.Invoice.constants.ServiceTypeConstants;
import com.api.workflow.yytex.Invoice.constants.TicketTypeConstants;

/**
 * 发票工具类 - 集成所有工具方法
 */
public class InvoiceUtil {

    //---------------------- 字符串工具方法 ----------------------

    /**
     * 检查字符串是否为空
     */
    public static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    //---------------------- 金额处理工具方法 ----------------------

    /**
     * 解析金额字符串为双精度浮点数
     */
    public static double parseAmount(String amountStr) {
        if (isEmpty(amountStr)) return 0.0;

        try {
            // 移除非数字字符（保留数字和小数点）
            String numericValue = amountStr.replaceAll("[^0-9.]", "");
            if (!numericValue.isEmpty()) {
                return Double.parseDouble(numericValue);
            }
        } catch (NumberFormatException e) {
            // 忽略无法解析的金额
        }
        return 0.0;
    }

    /**
     * 累加金额 - 统一处理数值解析和累加逻辑
     */
    public static double addNumericAmount(double total, String amount) {
        return total + parseAmount(amount);
    }

    //---------------------- JSON工具方法 ----------------------

    /**
     * 构建JSON请求体 - 用于发送到识别服务
     */
    public static String buildTicketJsonRequestBody(List<String> transportUrls, List<String> vatUrls,
                                                    List<String> mealUrls, List<String> taxiUrls) {
        StringBuilder jsonBody = new StringBuilder("[");

        // 添加交通票
        jsonBody.append("{\"ticketType\": 99,\"files\": [");
        appendUrlsAsJson(jsonBody, transportUrls);
        jsonBody.append("]},");

        // 添加增值税发票
        jsonBody.append("{\"ticketType\": 2,\"files\": [");
        appendUrlsAsJson(jsonBody, vatUrls);
        jsonBody.append("]},");

        // 添加餐费票据
        jsonBody.append("{\"ticketType\": 99,\"files\": [");
        appendUrlsAsJson(jsonBody, mealUrls);
        jsonBody.append("]},");

        // 添加打车票据
        jsonBody.append("{\"ticketType\": 99,\"files\": [");
        appendUrlsAsJson(jsonBody, taxiUrls);
        jsonBody.append("]}");

        jsonBody.append("]");
        return jsonBody.toString();
    }

    /**
     * 将URL列表添加为JSON格式
     */
    private static void appendUrlsAsJson(StringBuilder sb, List<String> urls) {
        if (urls == null || urls.isEmpty()) return;

        for (int i = 0; i < urls.size(); i++) {
            if (i > 0) sb.append(",");
            sb.append("\"").append(urls.get(i)).append("\"");
        }
    }

    /**
     * 构建JSON数组字符串
     */
    public static String buildJsonArrayString(List<String> items) {
        StringBuilder json = new StringBuilder();
        for (int i = 0; i < items.size(); i++) {
            if (i > 0) json.append(",");
            json.append("\"").append(items.get(i)).append("\"");
        }
        return json.toString();
    }

    //---------------------- OCR解析工具方法 ----------------------

    /**
     * 从数组提取所有word字段，拼接为逗号分隔的字符串
     */
    public static String extractArrayWords(JSONArray array) {
        if (array == null || array.isEmpty()) return "";

        StringBuilder items = new StringBuilder();
        for (int j = 0; j < array.size(); j++) {
            JSONObject item = array.getJSONObject(j);
            String word = item.getString("word");
            if (word != null && !word.equals("null")) {
                if (j > 0 && items.length() > 0) items.append(", ");
                items.append(word);
            }
        }
        return items.toString();
    }

    /**
     * 从数组中提取第一个word值
     */
    public static String extractFirstWord(JSONObject result, String fieldName) {
        if (!result.containsKey(fieldName)) return "";

        JSONArray array = result.getJSONArray(fieldName);
        if (array == null || array.isEmpty()) return "";

        JSONObject obj = array.getJSONObject(0);
        return obj.getString("word");
    }

    /**
     * 提取通用字段辅助方法
     */
    public static boolean extractField(JSONObject result,
                                       String sourceField,
                                       org.json.JSONObject target,
                                       String targetField) throws JSONException {
        if (result.containsKey(sourceField)) {
            JSONArray fieldArray = result.getJSONArray(sourceField);
            if (fieldArray != null && !fieldArray.isEmpty()) {
                JSONObject fieldObj = fieldArray.getJSONObject(0);
                target.put(targetField, fieldObj.getString("word"));
                return true;
            }
        }
        return false;
    }

    /**
     * 提取金额 - 统一处理不同字段名的金额提取
     */
    public static String extractAmount(JSONObject result) {
        // 尝试多种可能的金额字段名
        String[] possibleFields = {
                "ticket_rates", "amount", "total_amount", "price", "fee", "invoice_amount"
        };

        for (String field : possibleFields) {
            if (result.containsKey(field)) {
                JSONArray amountArray = result.getJSONArray(field);
                if (amountArray != null && !amountArray.isEmpty()) {
                    JSONObject amountObj = amountArray.getJSONObject(0);
                    return amountObj.getString("word");
                }
            }
        }

        return "";
    }

    /**
     * 判断商品是否为交通运输服务
     */
    public static boolean isCommodityTransportService(JSONObject result) {
        if (!result.containsKey("CommodityName")) return false;

        JSONArray commodityArray = result.getJSONArray("CommodityName");
        if (commodityArray == null || commodityArray.isEmpty()) return false;

        JSONObject commodityObj = commodityArray.getJSONObject(0);
        String commodityName = commodityObj.getString("word");

        return commodityName != null &&
                (commodityName.contains("运输服务") ||
                        commodityName.contains("客运服务") ||
                        commodityName.contains("打车") ||
                        commodityName.contains("出租车"));
    }

    /**
     * 提取航空公司和航班号
     */
    public static String extractCarrierAndFlight(JSONObject result) {
        String carrier = extractFirstWord(result, "carrier");
        String flight = extractFirstWord(result, "flight");

        if (!flight.isEmpty()) {
            if (!carrier.isEmpty()) {
                return carrier + "-" + flight;
            } else {
                return flight;
            }
        }

        return null;
    }

    /**
     * 判断商品是否为租车服务
     */
    public static boolean isCommodityRentalCarService(JSONObject result) {
        String commodityName = extractFirstWord(result, "CommodityName");
        return commodityName != null &&
                (commodityName.contains("*经营租赁*租车费") ||
                        (commodityName.contains("经营租赁") && commodityName.contains("租车")) ||
                        commodityName.contains("汽车租赁") ||
                        commodityName.contains("车辆租赁")) &&
                // 排除通行费相关的内容
                !commodityName.contains("通行费") &&
                !commodityName.contains("过路费") &&
                !commodityName.contains("过桥费");
    }

    /**
     * 判断是否为成品油发票（私车公用）
     * 通过InvoiceTag为"成品油"或CommodityName包含"*汽油*汽油"来识别
     */
    public static boolean isFuelInvoice(JSONObject result, String invoiceTag) {
        // 检查InvoiceTag字段
        if ("成品油".equals(invoiceTag)) {
            return true;
        }

        // 检查CommodityName字段
        String commodityName = extractFirstWord(result, "CommodityName");
        return commodityName != null &&
                (commodityName.contains("*汽油*汽油") ||
                        commodityName.contains("*柴油*柴油") ||
                        commodityName.contains("*天然气*天然气") ||
                        commodityName.contains("汽油") ||
                        commodityName.contains("柴油") ||
                        commodityName.contains("燃油"));
    }

    /**
     * 判断是否为通行费发票（私车公用）
     * 通过InvoiceTag为"通行费"或CommodityName包含"*经营租赁*通行费"来识别
     */
    public static boolean isTollInvoice(JSONObject result, String invoiceTag) {
        // 检查InvoiceTag字段
        if ("通行费".equals(invoiceTag)) {
            return true;
        }

        // 检查CommodityName字段
        String commodityName = extractFirstWord(result, "CommodityName");
        return commodityName != null &&
                (commodityName.contains("*经营租赁*通行费") ||
                        commodityName.contains("通行费") ||
                        commodityName.contains("过路费") ||
                        commodityName.contains("过桥费") ||
                        commodityName.contains("高速费"));
    }

    //---------------------- 票据类型工具方法 ----------------------

    /**
     * 票据类型格式化 - 将英文类型转换为中文显示
     */
    public static String formatTicketType(String type) {
        // 将类型如 "train_ticket" 格式化为 "火车"
        if (type == null) return "未知交通工具";

        // 交通票类型映射
        if ("train_ticket".equals(type)) return "火车";
        if ("air_ticket".equals(type)) return "飞机";
        if ("bus_ticket".equals(type)) return "汽车";
        if ("ferry_ticket".equals(type)) return "船票";

        // 打车票类型映射
        if ("taxi_receipt".equals(type)) return "出租车";
        if ("taxi_online_ticket".equals(type)) return "网约车";
        if ("toll_invoice".equals(type)) return "过路过桥费";

        // 餐费票据类型映射
        if ("quota_invoice".equals(type)) return "定额发票";
        if ("roll_normal_invoice".equals(type)) return "卷票";
        if ("printed_invoice".equals(type)) return "机打发票";
        if ("printed_elec_invoice".equals(type)) return "机打电子发票";
        if ("limit_invoice".equals(type)) return "限额发票";
        if ("shopping_receipt".equals(type)) return "购物小票";
        if ("pos_invoice".equals(type)) return "POS小票";

        // 增值税发票类型映射
        if ("vat_invoice".equals(type)) return "增值税发票";
        if ("motor_vehicle_invoice".equals(type)) return "机动车销售发票";
        if ("used_vehicle_invoice".equals(type)) return "二手车发票";

        // 其他类型
        if ("others".equals(type)) return "其他票据";

        // 无法识别的类型，返回格式化后的字符串
        return type.replace("_ticket", "").replace("_invoice", "").replace("_receipt", "").replace("_", " ");
    }

    /**
     * 获取职位字段前缀
     */
    public static String getPositionPrefix(String positionCode) {
        switch (positionCode) {
            case "0": return "ywy"; // 业务员
            case "1": return "bmfzr"; // 部门负责人
            case "2": return "fz"; // 副总
            case "3": return "zjl"; // 总经理
            case "4": return "gdy"; // 跟单员
            default: return null;
        }
    }

    /**
     * 根据职位代码获取职位名称
     */
    public static String mapPositionToName(String positionCode) {
        switch (positionCode) {
            case "0": return "业务员";
            case "1": return "部门负责人";
            case "2": return "副总";
            case "3": return "总经理";
            case "4": return "跟单员";
            default: return "未知职位";
        }
    }

    //---------------------- 票据处理工具方法 ----------------------

    /**
     * 获取附件ID列表
     */
    public static List<Integer> getAttachmentIds(RecordSet rs, String attachIds) {
        List<Integer> fileIds = new ArrayList<>();

        if (attachIds == null || attachIds.isEmpty()) {
            return fileIds;
        }

        try {
            rs.execute("select imagefileid from DocImageFile where docid in (" + attachIds + ")");
            while (rs.next()) {
                fileIds.add(rs.getInt("imagefileid"));
            }
        } catch (Exception e) {
            // 忽略错误
        }

        return fileIds;
    }

    /**
     * 获取文件下载URL
     */
    public static List<String> getFileDownUrls(List<Integer> fileIds, String SERVER_IP) throws Exception {
        List<String> urls = new ArrayList<>();

        if (fileIds.isEmpty()) {
            return urls;
        }

        weaver.docs.docs.util.DesUtils des = new weaver.docs.docs.util.DesUtils();

        for (Integer fileId : fileIds) {
            try {
                String ddcode = "1_" + fileId;
                ddcode = des.encrypt(ddcode);
                String fileDownUrl = String.format(
                        "%s/weaver/weaver.file.FileDownload?fileid=%s&download=1&ddcode=%s",
                        SERVER_IP, fileId, ddcode
                );
                urls.add(fileDownUrl);
            } catch (Exception e) {
                // 忽略单个URL生成错误
            }
        }

        return urls;
    }

    /**
     * 提取日期时间字段
     */
    public static void extractDateTime(com.alibaba.fastjson.JSONObject result, org.json.JSONObject ticket) throws JSONException {
        String dateTime = "";
        if (result.containsKey("date")) {
            com.alibaba.fastjson.JSONArray dateArray = result.getJSONArray("date");
            if (dateArray != null && !dateArray.isEmpty()) {
                com.alibaba.fastjson.JSONObject dateObj = dateArray.getJSONObject(0);
                dateTime = dateObj.getString("word");
            }
        }

        if (result.containsKey("time")) {
            com.alibaba.fastjson.JSONArray timeArray = result.getJSONArray("time");
            if (timeArray != null && !timeArray.isEmpty()) {
                com.alibaba.fastjson.JSONObject timeObj = timeArray.getJSONObject(0);
                if (!dateTime.isEmpty()) dateTime += " ";
                dateTime += timeObj.getString("word");
            }
        }
        ticket.put("dateTime", dateTime);
    }

    /**
     * 从JSONObject中提取全部商品名称并用逗号连接
     * @param result JSON数据
     * @param fieldName 字段名称
     * @return 提取的字符串
     */
    public static String extractCommodityNames(com.alibaba.fastjson.JSONObject result, String fieldName) {
        StringBuilder items = new StringBuilder();
        if (result.containsKey(fieldName)) {
            com.alibaba.fastjson.JSONArray commodityArray = result.getJSONArray(fieldName);
            if (commodityArray != null && !commodityArray.isEmpty()) {
                for (int j = 0; j < commodityArray.size(); j++) {
                    com.alibaba.fastjson.JSONObject itemObj = commodityArray.getJSONObject(j);
                    String word = itemObj.getString("word");
                    if (word != null && !word.equals("null")) {
                        if (j > 0 && !items.toString().isEmpty()) items.append(", ");
                        items.append(word);
                    }
                }
            }
        }
        return items.toString();
    }

    /**
     * 从JSONObject中提取全部项目名称并用逗号连接
     * @param result JSON数据
     * @param fieldName 字段名称
     * @return 提取的字符串
     */
    public static String extractItemNames(com.alibaba.fastjson.JSONObject result, String fieldName) {
        StringBuilder items = new StringBuilder();
        if (result.containsKey(fieldName)) {
            com.alibaba.fastjson.JSONArray itemArray = result.getJSONArray(fieldName);
            if (itemArray != null && !itemArray.isEmpty()) {
                for (int j = 0; j < itemArray.size(); j++) {
                    com.alibaba.fastjson.JSONObject itemObj = itemArray.getJSONObject(j);
                    if (j > 0) items.append(", ");
                    items.append(itemObj.getString("word"));
                }
            }
        }
        return items.toString();
    }

    /**
     * 提取并连接PickupTime和DropoffTime字段作为行程时间
     * @param result JSON数据
     * @return 行程时间字符串
     */
    public static String extractJourneyTime(com.alibaba.fastjson.JSONObject result) {
        if (result.containsKey("PickupTime") && result.containsKey("DropoffTime")) {
            String pickupTime = extractFirstWord(result, "PickupTime");
            String dropoffTime = extractFirstWord(result, "DropoffTime");
            return pickupTime + "-" + dropoffTime;
        } else if (result.containsKey("Time")) {
            return extractFirstWord(result, "Time");
        }
        return "";
    }

    /**
     * 提取座位类型
     * @param result JSON数据
     * @param type 票据类型
     * @return 座位类型
     * @throws JSONException JSON异常
     */
    public static String extractSeatType(com.alibaba.fastjson.JSONObject result, String type) throws JSONException {
        // 尝试各种可能的座位类型字段
        if (result.containsKey("seat_category")) {
            com.alibaba.fastjson.JSONArray seatArray = result.getJSONArray("seat_category");
            if (seatArray != null && !seatArray.isEmpty()) {
                com.alibaba.fastjson.JSONObject seatObj = seatArray.getJSONObject(0);
                return seatObj.getString("word");
            }
        }
        else if (result.containsKey("class")) {
            com.alibaba.fastjson.JSONArray classArray = result.getJSONArray("class");
            if (classArray != null && !classArray.isEmpty()) {
                com.alibaba.fastjson.JSONObject classObj = classArray.getJSONObject(0);
                return formatTicketType(type) + "-" + classObj.getString("word") + "舱";
            }
        }

        // 默认返回格式化的票据类型
        return formatTicketType(type);
    }
}