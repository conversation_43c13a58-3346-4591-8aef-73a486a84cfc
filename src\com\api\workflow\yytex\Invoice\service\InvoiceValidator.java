package com.api.workflow.yytex.Invoice.service;

import org.json.JSONObject;
import org.json.JSONException;
import java.util.*;
import weaver.conn.RecordSet;

import com.api.workflow.yytex.Invoice.constants.FieldConstants;
import com.api.workflow.yytex.Invoice.constants.TableConstants;
import com.api.workflow.yytex.Invoice.util.InvoiceUtil;

/**
 * 发票验证器 - 负责检查发票是否重复
 */
public class InvoiceValidator {

    /**
     * 检查发票号是否重复
     */
    public JSONObject checkDuplicateInvoices(List<String> allInvoiceNumbers, String requestid) throws JSONException {
        if (allInvoiceNumbers == null || allInvoiceNumbers.isEmpty()) {
            // 没有发票号，直接返回成功
            JSONObject response = new JSONObject();
            response.put("success", true);
            return response;
        }

        // 用于存储重复的发票号和表名
        Map<String, String> duplicates = new HashMap<>();

        // 第一步：检查当前表单内是否有重复的发票号
        Map<String, String> currentInvoices = new HashMap<>(); // 用于存储当前表单内的发票号及其类型

        // 检查当前表单内重复的逻辑
        // 遍历所有发票号，看是否在currentInvoices中已经存在
        for (String invoiceNum : allInvoiceNumbers) {
            if (InvoiceUtil.isEmpty(invoiceNum)) continue;

            if (currentInvoices.containsKey(invoiceNum)) {
                // 发现当前表单内重复
                duplicates.put(invoiceNum, "当前表单内发现重复");
            } else {
                currentInvoices.put(invoiceNum, "当前表单");
            }
        }

        // 如果当前表单内有重复发票，直接返回错误
        if (!duplicates.isEmpty()) {
            return buildDuplicateErrorResponse(duplicates);
        }

        // 第二步：检查数据库中是否有重复的发票号
        // 构建用于SQL IN子句的发票号字符串
        StringBuilder invoiceNumbersStr = new StringBuilder();
        for (int i = 0; i < allInvoiceNumbers.size(); i++) {
            String invoiceNum = allInvoiceNumbers.get(i);
            if (InvoiceUtil.isEmpty(invoiceNum)) continue;

            if (invoiceNumbersStr.length() > 0) {
                invoiceNumbersStr.append(",");
            }
            // 对特殊字符进行转义，防止SQL注入
            invoiceNumbersStr.append("'").append(invoiceNum.replace("'", "''")).append("'");
        }

        if (invoiceNumbersStr.length() > 0) {
            // 获取当前请求的mainid
            String currentMainId = null;
            RecordSet rs = new RecordSet();

            if (!InvoiceUtil.isEmpty(requestid)) {
                String mainIdSql = "SELECT " + FieldConstants.ID + " FROM " + TableConstants.MAIN_TABLE +
                        " WHERE " + FieldConstants.REQUEST_ID + " = ?";
                rs.executeQuery(mainIdSql, requestid);
                if (rs.next()) {
                    currentMainId = rs.getString(FieldConstants.ID);
                }
            }

            // 构建排除当前表单的SQL条件
            String mainIdCondition = "";
            if (!InvoiceUtil.isEmpty(currentMainId)) {
                mainIdCondition = " AND " + FieldConstants.MAIN_ID + " != '" +
                        currentMainId.replace("'", "''") + "'";
            }

            // 检查数据库中的各种表
            checkDatabaseTable(rs, invoiceNumbersStr.toString(), mainIdCondition,
                    TableConstants.TRANSPORT_TABLE, FieldConstants.TRANSPORT_INVOICE_NUM, "交通票据", duplicates);

            checkDatabaseTable(rs, invoiceNumbersStr.toString(), mainIdCondition,
                    TableConstants.VAT_TABLE, FieldConstants.INVOICE_NUM, "增值税发票", duplicates);

            checkDatabaseTable(rs, invoiceNumbersStr.toString(), mainIdCondition,
                    TableConstants.MEAL_TABLE, FieldConstants.INVOICE_NUM, "餐费票据", duplicates);

            checkDatabaseTable(rs, invoiceNumbersStr.toString(), mainIdCondition,
                    TableConstants.TAXI_TABLE, FieldConstants.INVOICE_NUM, "打车票据", duplicates);

            // 如果数据库中有重复发票，返回错误
            if (!duplicates.isEmpty()) {
                return buildDuplicateErrorResponse(duplicates);
            }
        }

        // 没有发现重复，返回成功
        JSONObject response = new JSONObject();
        response.put("success", true);
        return response;
    }

    /**
     * 检查数据库表中是否有重复发票
     */
    private void checkDatabaseTable(RecordSet rs, String invoiceNumbersStr, String mainIdCondition,
                                    String tableName, String fieldName, String typeLabel,
                                    Map<String, String> duplicates) {
        // 检查指定表中的发票号
        String sql = "SELECT " + fieldName + " FROM " + tableName +
                " WHERE " + fieldName + " IN (" + invoiceNumbersStr + ")" + mainIdCondition;
        try {
            rs.execute(sql);
            while (rs.next()) {
                String invoiceNum = rs.getString(fieldName);
                if (!InvoiceUtil.isEmpty(invoiceNum)) {
                    duplicates.put(invoiceNum, typeLabel);
                }
            }
        } catch (Exception e) {
            // 忽略查询异常
        }
    }

    /**
     * 构建重复发票错误响应
     */
    private JSONObject buildDuplicateErrorResponse(Map<String, String> duplicates) throws JSONException {
        // 构建重复发票提示信息
        StringBuilder duplicateMsg = new StringBuilder("发现重复发票：");
        int count = 0;
        for (Map.Entry<String, String> entry : duplicates.entrySet()) {
            if (count > 0) duplicateMsg.append("，");
            duplicateMsg.append("发票号 ").append(entry.getKey())
                    .append(" 在").append(entry.getValue()).append("中已存在");
            count++;
        }

        // 返回错误信息
        JSONObject response = new JSONObject();
        response.put("success", false);
        response.put("message", duplicateMsg.toString());
        return response;
    }
}